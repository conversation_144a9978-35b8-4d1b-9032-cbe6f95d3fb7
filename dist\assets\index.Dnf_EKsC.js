import{c as M,q as O,__tla as R}from"./index.BSP3cg_z.js";import{d as C,A as S,k as y,c as U,o as z,B as t,a as L,b,f as q,t as l,D as J,u as o,H as K,v as d,g as Q,E as W,G as X,P as Y,N as Z}from"./vue.CnN__PXn.js";let B,ee=Promise.all([(()=>{try{return R}catch{}})()]).then(async()=>{let m,g,_,k,v;m={class:"head-container"},g={class:"head-container-header"},_={class:"head-container-header-input"},k={key:0,class:"head-container-header-dropdown"},v=C({name:"query-tree"}),B=O(C({...v,props:{props:{type:Object,default:()=>({label:"name",children:"children",value:"id"})},placeholder:{type:String,default:""},loading:{type:Boolean,default:!1},query:{type:Function,required:!0},showExpand:{type:Boolean,default:!1}},emits:["search","nodeClick"],setup(u,{expose:E,emit:F}){const N=F,n=u,r=S({List:[],localLoading:n.loading}),x=y(),s=y(),i=y(!0),V=U(()=>["!h-[20px]","reset-margin","!text-gray-500","dark:!text-white","dark:hover:!text-primary"]),j=e=>{N("nodeClick",e)},f=()=>{if(n.query instanceof Function){r.localLoading=!0;const e=n.query(o(s));typeof e!="object"&&typeof e!="function"||typeof e.then!="function"||e.then(a=>{r.List=a.data}).catch(a=>{M().error(a.msg)})}};return z(()=>{f()}),E({getdeptTree:f}),(e,a)=>{const A=t("el-input"),T=t("MoreFilled"),$=t("el-icon"),D=t("el-button"),P=t("el-dropdown-item"),G=t("el-dropdown-menu"),H=t("el-dropdown"),I=t("el-tree");return b(),L("div",m,[q("div",g,[q("div",_,[l(A,{modelValue:o(s),"onUpdate:modelValue":a[0]||(a[0]=c=>K(s)?s.value=c:null),"suffix-icon":"search",placeholder:u.placeholder,clearable:"",onChange:f},null,8,["modelValue","placeholder"])]),u.showExpand?(b(),L("div",k,[l(H,{"hide-on-click":!1},{dropdown:d(()=>[l(G,null,{default:d(()=>[l(P,null,{default:d(()=>[l(D,{class:Q(o(V)),link:"",type:"primary",icon:o(i)?"expand":"fold",onClick:a[1]||(a[1]=c=>(p=>{i.value=p;const w=x.value.store._getAllNodes();for(let h=0;h<w.length;h++)w[h].expanded=p})(!o(i)))},{default:d(()=>[W(X(o(i)?"\u6298\u53E0":"\u5C55\u5F00"),1)]),_:1},8,["class","icon"])]),_:1})]),_:1})]),default:d(()=>[l($,{style:{transform:"rotate(90deg)"}},{default:d(()=>[l(T)]),_:1})]),_:1})])):J("",!0)]),l(I,{class:"mt20",data:o(r).List,props:n.props,"expand-on-click-node":!1,ref_key:"deptTreeRef",ref:x,loading:o(r).localLoading,"node-key":"id","highlight-current":"","default-expand-all":"",onNodeClick:j},Y({_:2},[e.$slots.default?{name:"default",fn:d(({node:c,data:p})=>[Z(e.$slots,"default",{node:c,data:p},void 0,!0)]),key:"0"}:void 0]),1032,["data","props","loading"])])}}}),[["__scopeId","data-v-f4d0695c"]])});export{ee as __tla,B as default};
