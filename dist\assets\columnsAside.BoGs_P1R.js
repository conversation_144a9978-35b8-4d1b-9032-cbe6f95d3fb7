import{t as K,u as Q,e as o,x as U,q as V,__tla as W}from"./index.BSP3cg_z.js";import{d as w,k as R,s as q,l as X,z as Y,A as Z,o as ee,i as le,O as te,w as ne,B as z,a as m,b as c,t as I,v as se,f as h,F as ae,p as ie,u as i,g as y,G as B,y as oe}from"./vue.CnN__PXn.js";let E,ue=Promise.all([(()=>{try{return W}catch{}})()]).then(async()=>{let L,H,_,O,g,$;L={class:"layout-columns-aside"},H=["onClick","onMouseenter","title"],_={class:"columns-vertical-title font12"},O=["href"],g={class:"columns-vertical-title font12"},$=w({name:"layoutColumnsAside"}),E=V(w({...$,setup(re){const S=R([]),b=R(),f=K(),F=Q(),{routesList:k,isColumnsMenuHover:G,isColumnsNavHover:J}=q(f),{themeConfig:u}=q(F),M=X(),P=Y(),t=Z({columnsAsideList:[],liIndex:0,liOldIndex:null,liHoverIndex:null,liOldPath:null,difference:0,routeSplit:[]}),N=e=>{t.liIndex=e,b.value.style.top=`${S.value[e].offsetTop+t.difference}px`},j=e=>{oe(()=>{N(e)})},d=e=>{const n=x(k.value,e);let a={children:[]};return t.columnsAsideList.map((s,l)=>{s.path===n.path&&(s.k=l,a.item={...s},a.children=[{...s}],s.children&&(a.children=s.children))}),a},D=e=>e.filter(n=>{var a;return!((a=n.meta)!=null&&a.isHide)}).map(n=>((n=Object.assign({},n)).children&&(n.children=D(n.children)),n)),x=(e,n)=>{let a;return e.forEach(s=>{s.path!==n?s.children&&x(s.children,n)&&(a=s):a=s}),a};return ee(()=>{(()=>{var n;t.columnsAsideList=D(k.value);const e=d(M.path);if(Object.keys(e).length<=0)return!1;j((n=e.item)==null?void 0:n.k),o.emit("setSendColumnsChildren",e)})(),o.on("restoreDefault",()=>{t.liOldIndex=null,t.liOldPath=null})}),le(()=>{o.off("restoreDefault",()=>{})}),te(e=>{(n=>{const a=x(k.value,n),s=t.columnsAsideList.find(l=>l.path===a.path);if(!s)return!1;setTimeout(()=>{j(s.k)},0)})(e.path),o.emit("setSendColumnsChildren",d(e.path))}),ne(U.state,e=>{if(e.themeConfig.themeConfig.columnsAsideStyle==="columnsRound"?t.difference=3:t.difference=0,e.routesList.isColumnsMenuHover||e.routesList.isColumnsNavHover){if(t.liHoverIndex=t.liOldIndex,!t.liOldPath)return!1;o.emit("setSendColumnsChildren",d(t.liOldPath))}else t.liHoverIndex=null,o.emit("setSendColumnsChildren",d(M.path))},{deep:!0}),(e,n)=>{const a=z("SvgIcon"),s=z("el-scrollbar");return c(),m("div",L,[I(s,null,{default:se(()=>[h("ul",{onMouseleave:n[0]||(n[0]=l=>(async()=>{await f.setColumnsNavHover(!1),setTimeout(()=>{G||J||o.emit("restoreDefault")},100)})())},[(c(!0),m(ae,null,ie(i(t).columnsAsideList,(l,r)=>(c(),m("li",{key:r,onClick:v=>((A,p)=>{N(p);let{path:C,redirect:T}=A;T?P.push(T):P.push(C)})(l,r),onMouseenter:v=>((A,p)=>{if(!u.value.isColumnsMenuHoverPreload)return!1;let{path:C}=A;t.liOldPath=C,t.liOldIndex=p,t.liHoverIndex=p,o.emit("setSendColumnsChildren",d(C)),f.setColumnsMenuHover(!1),f.setColumnsNavHover(!0)})(l,r),ref_for:!0,ref:v=>{v&&(i(S)[r]=v)},class:y({"layout-columns-active":i(t).liIndex===r,"layout-columns-hover":i(t).liHoverIndex===r}),title:e.$t(l.name)},[!l.meta.isLink||l.meta.isLink&&l.meta.isIframe?(c(),m("div",{key:0,class:y(i(u).columnsAsideLayout)},[I(a,{name:l.meta.icon},null,8,["name"]),h("div",_,B(e.$t(l.name)&&e.$t(l.name).length>=4?e.$t(l.name).substr(0,i(u).columnsAsideLayout==="columns-vertical"?4:3):e.$t(l.name)),1)],2)):(c(),m("div",{key:1,class:y(i(u).columnsAsideLayout)},[h("a",{href:l.meta.isLink,target:"_blank"},[I(a,{name:l.meta.icon},null,8,["name"]),h("div",g,B(e.$t(l.name)&&e.$t(l.name).length>=4?e.$t(l.name).substr(0,i(u).columnsAsideLayout==="columns-vertical"?4:3):e.$t(l.name)),1)],8,O)],2))],42,H))),128)),h("div",{ref_key:"columnsAsideActiveRef",ref:b,class:y(i(u).columnsAsideStyle)},null,2)],32)]),_:1})])}}}),[["__scopeId","data-v-f6687df1"]])});export{ue as __tla,E as default};
