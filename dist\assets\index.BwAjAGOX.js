import{u as m,q as n,__tla as p}from"./index.BSP3cg_z.js";import{d as e,s as _,c as d,a as i,b as v,f as r,G as y,u as h}from"./vue.CnN__PXn.js";let l,b=Promise.all([(()=>{try{return p}catch{}})()]).then(async()=>{let a,t,s,o;a={class:"layout-footer pb5"},t={class:"layout-footer-warp"},s={class:"mt5"},o=e({name:"layoutFooter"}),l=n(e({...o,setup(g){const u=m(),{themeConfig:c}=_(u),f=d(()=>c.value.footerAuthor);return(q,w)=>(v(),i("div",a,[r("div",t,[r("div",s,y(h(f)),1)])]))}}),[["__scopeId","data-v-768c7ee2"]])});export{b as __tla,l as default};
