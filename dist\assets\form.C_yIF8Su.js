const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.CyScsmYB.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/index.B9s4Ea2a.css"])))=>i.map(i=>d[i]);
import{v as H,r as y,a as M,i as W,p as z,g as J,h as K,c as k,__tla as Q}from"./index.BSP3cg_z.js";import{d as I,k as V,A as U,B as s,m as X,e as i,b as p,v as o,q as Y,u as a,t,D as c,E as n,j as Z,f as ee,G as x,H as le,y as ae}from"./vue.CnN__PXn.js";let D,re=Promise.all([(()=>{try{return Q}catch{}})()]).then(async()=>{let v,T;v={class:"dialog-footer"},T=I({name:"systemMenuDialog"}),D=I({...T,emits:["refresh"],setup(oe,{expose:q,emit:N}){const O=N,{t:w}=H.useI18n(),A=Z(()=>M(()=>import("./index.CyScsmYB.js").then(async r=>(await r.__tla,r)),__vite__mapDeps([0,1,2,3,4]))),b=V(!1),h=V(!1),_=V(),l=U({ruleForm:{menuId:"",name:"",enName:"",permission:"",parentId:"",icon:"",path:"",sortOrder:0,menuType:"1",keepAlive:"0",visible:"1",embedded:"0"},parentData:[]}),L=U({menuType:[{required:!0,message:"\u83DC\u5355\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],parentId:[{required:!0,message:"\u4E0A\u7EA7\u83DC\u5355\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{validator:y.overLength,trigger:"blur"},{required:!0,message:"\u83DC\u5355\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],path:[{validator:y.overLength,trigger:"blur"},{required:!0,message:"\u8DEF\u5F84\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],icon:[{required:!0,message:"\u56FE\u6807\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],permission:[{validator:y.overLength,trigger:"blur"},{required:!0,message:"\u6743\u9650\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sortOrder:[{validator:y.overLength,trigger:"blur"},{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],enName:[{validator:y.overLength,trigger:"blur"}]}),B=r=>{W(r).then(e=>{Object.assign(l.ruleForm,e.data)})},P=()=>{l.parentData=[],z({type:"0"}).then(r=>{let e={id:"-1",name:"\u6839\u83DC\u5355",children:[]};e.children=r.data,l.parentData.push(e)})},E=async()=>{if(!await _.value.validate().catch(()=>{}))return!1;try{h.value=!0,l.ruleForm.menuId?await J(l.ruleForm):await K(l.ruleForm),k().success(w(l.ruleForm.menuId?"common.editSuccessText":"common.addSuccessText")),b.value=!1,O("refresh")}catch(r){k().error(r.msg)}finally{h.value=!1}};return q({openDialog:(r,e)=>{l.ruleForm.menuId="",b.value=!0,ae(()=>{var d;(d=_.value)==null||d.resetFields(),l.ruleForm.parentId=(e==null?void 0:e.id)||"-1"}),e!=null&&e.id&&r==="edit"&&(l.ruleForm.menuId=e.id,B(e.id)),P()}}),(r,e)=>{const d=s("el-radio"),f=s("el-radio-group"),m=s("el-form-item"),j=s("el-tree-select"),F=s("el-input"),C=s("el-input-number"),R=s("el-form"),$=s("el-button"),S=s("el-dialog"),G=X("loading");return p(),i(S,{title:a(l).ruleForm.menuId?r.$t("common.editBtn"):r.$t("common.addBtn"),width:"600",modelValue:a(b),"onUpdate:modelValue":e[12]||(e[12]=g=>le(b)?b.value=g:null),"close-on-click-modal":!1,"destroy-on-close":!0,draggable:""},{footer:o(()=>[ee("span",v,[t($,{onClick:e[11]||(e[11]=g=>b.value=!1)},{default:o(()=>[n(x(r.$t("common.cancelButtonText")),1)]),_:1}),t($,{type:"primary",onClick:E,disabled:a(h)},{default:o(()=>[n(x(r.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:o(()=>[Y((p(),i(R,{ref_key:"menuDialogFormRef",ref:_,model:a(l).ruleForm,rules:a(L),"label-width":"90px"},{default:o(()=>{var g;return[t(m,{label:r.$t("sysmenu.menuType"),prop:"menuType"},{default:o(()=>[t(f,{modelValue:a(l).ruleForm.menuType,"onUpdate:modelValue":e[0]||(e[0]=u=>a(l).ruleForm.menuType=u)},{default:o(()=>[t(d,{border:"",label:"0"},{default:o(()=>e[13]||(e[13]=[n("\u83DC\u5355")])),_:1}),t(d,{border:"",label:"1"},{default:o(()=>e[14]||(e[14]=[n("\u6309\u94AE")])),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(m,{label:r.$t("sysmenu.parentId"),prop:"parentId"},{default:o(()=>[t(j,{modelValue:a(l).ruleForm.parentId,"onUpdate:modelValue":e[1]||(e[1]=u=>a(l).ruleForm.parentId=u),data:a(l).parentData,"render-after-expand":!1,props:{value:"id",label:"name",children:"children"},class:"w100",clearable:"","check-strictly":"",placeholder:r.$t("sysmenu.inputParentIdTip")},null,8,["modelValue","data","placeholder"])]),_:1},8,["label"]),t(m,{label:r.$t("sysmenu.name"),prop:"name"},{default:o(()=>[t(F,{modelValue:a(l).ruleForm.name,"onUpdate:modelValue":e[2]||(e[2]=u=>a(l).ruleForm.name=u),clearable:"",placeholder:r.$t("sysmenu.inputNameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(m,{label:r.$t("sysmenu.enName"),prop:"enName"},{default:o(()=>[t(F,{modelValue:a(l).ruleForm.enName,"onUpdate:modelValue":e[3]||(e[3]=u=>a(l).ruleForm.enName=u),clearable:"",placeholder:r.$t("sysmenu.inputEnNameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),a(l).ruleForm.menuType==="0"?(p(),i(m,{key:0,label:r.$t("sysmenu.path"),prop:"path"},{default:o(()=>[t(F,{modelValue:a(l).ruleForm.path,"onUpdate:modelValue":e[4]||(e[4]=u=>a(l).ruleForm.path=u),placeholder:r.$t("sysmenu.inputPathTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])):c("",!0),a(l).ruleForm.menuType==="1"?(p(),i(m,{key:1,label:r.$t("sysmenu.permission"),prop:"permission"},{default:o(()=>[t(F,{modelValue:a(l).ruleForm.permission,"onUpdate:modelValue":e[5]||(e[5]=u=>a(l).ruleForm.permission=u),maxlength:"50",placeholder:r.$t("sysmenu.inputPermissionTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])):c("",!0),t(m,{label:r.$t("sysmenu.sortOrder"),prop:"sortOrder"},{default:o(()=>[t(C,{modelValue:a(l).ruleForm.sortOrder,"onUpdate:modelValue":e[6]||(e[6]=u=>a(l).ruleForm.sortOrder=u),min:0,"controls-position":"right"},null,8,["modelValue"])]),_:1},8,["label"]),a(l).ruleForm.menuType==="0"?(p(),i(m,{key:2,label:r.$t("sysmenu.icon"),prop:"icon"},{default:o(()=>[t(a(A),{placeholder:r.$t("sysmenu.inputIconTip"),modelValue:a(l).ruleForm.icon,"onUpdate:modelValue":e[7]||(e[7]=u=>a(l).ruleForm.icon=u)},null,8,["placeholder","modelValue"])]),_:1},8,["label"])):c("",!0),a(l).ruleForm.menuType==="0"&&((g=a(l).ruleForm.path)!=null&&g.startsWith("http"))?(p(),i(m,{key:3,label:r.$t("sysmenu.embedded"),prop:"embedded"},{default:o(()=>[t(f,{modelValue:a(l).ruleForm.embedded,"onUpdate:modelValue":e[8]||(e[8]=u=>a(l).ruleForm.embedded=u)},{default:o(()=>[t(d,{border:"",label:"0"},{default:o(()=>e[15]||(e[15]=[n("\u5426")])),_:1}),t(d,{border:"",label:"1"},{default:o(()=>e[16]||(e[16]=[n("\u662F")])),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])):c("",!0),a(l).ruleForm.menuType==="0"?(p(),i(m,{key:4,label:r.$t("sysmenu.keepAlive"),prop:"keepAlive"},{default:o(()=>[t(f,{modelValue:a(l).ruleForm.keepAlive,"onUpdate:modelValue":e[9]||(e[9]=u=>a(l).ruleForm.keepAlive=u)},{default:o(()=>[t(d,{border:"",label:"0"},{default:o(()=>e[17]||(e[17]=[n("\u5426")])),_:1}),t(d,{border:"",label:"1"},{default:o(()=>e[18]||(e[18]=[n("\u662F")])),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])):c("",!0),a(l).ruleForm.menuType==="0"?(p(),i(m,{key:5,label:r.$t("sysmenu.visible"),prop:"visible"},{default:o(()=>[t(f,{modelValue:a(l).ruleForm.visible,"onUpdate:modelValue":e[10]||(e[10]=u=>a(l).ruleForm.visible=u)},{default:o(()=>[t(d,{border:"",label:"0"},{default:o(()=>e[19]||(e[19]=[n("\u5426")])),_:1}),t(d,{border:"",label:"1"},{default:o(()=>e[20]||(e[20]=[n("\u662F")])),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])):c("",!0)]}),_:1},8,["model","rules"])),[[G,a(h)]])]),_:1},8,["title","modelValue"])}}})});export{re as __tla,D as default};
