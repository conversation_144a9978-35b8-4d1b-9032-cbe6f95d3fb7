const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/subItem.CPtLQzhs.js","assets/vue.CnN__PXn.js","assets/index.BSP3cg_z.js","assets/index.DjeikzAe.css"])))=>i.map(i=>d[i]);
import{t as Q,u as T,o as m,a as V,e as W,q as Y,__tla as J}from"./index.BSP3cg_z.js";import{d as C,k as K,s as M,l as N,A as U,c as X,h as Z,o as ee,O as ae,B as d,a as y,b as f,t as c,v as u,u as o,F as x,p as te,e as z,j as le,f as D,G as k,P as ne,E,Q as R,y as se}from"./vue.CnN__PXn.js";let S,ie=Promise.all([(()=>{try{return J}catch{}})()]).then(async()=>{let A,L,w;A={class:"el-menu-horizontal-warp"},L=["onClick"],w=C({name:"navMenuHorizontal"}),S=Y(C({...w,props:{menuList:{type:Array,default:()=>[]}},setup(H){const O=le(()=>V(()=>import("./subItem.CPtLQzhs.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([0,1,2,3]))),P=H,h=K(),$=Q(),j=T(),{routesList:p}=M($),{themeConfig:I}=M(j),q=N(),r=U({defaultActive:""}),B=X(()=>P.menuList),F=t=>{const e=t.wheelDelta||40*-t.deltaY;h.value.$refs.wrapRef.scrollLeft=h.value.$refs.wrapRef.scrollLeft+e/4},b=t=>t.filter(e=>{var a;return!((a=e.meta)!=null&&a.isHide)}).map(e=>((e=Object.assign({},e)).children&&(e.children=b(e.children)),e)),g=t=>{const{path:e,meta:a}=t,n=v(p.value,e);if(I.value.layout==="classic")r.defaultActive=n?n.path:`/${e==null?void 0:e.split("/")[1]}`;else{const i=a!=null&&a.isDynamic?a.isDynamicPath.split("/"):e.split("/");i.length>=4&&(a!=null&&a.isHide)?r.defaultActive=i.splice(0,3).join("/"):r.defaultActive=e}},v=(t,e)=>{let a;return t.forEach(n=>{n.path!==e?n.children&&v(n.children,e)&&(a=n):a=n}),a};return Z(()=>{g(q)}),ee(()=>{se(()=>{let t=document.querySelector(".el-menu.el-menu--horizontal li.is-active");if(!t)return!1;h.value.$refs.wrapRef.scrollLeft=t.offsetLeft})}),ae(t=>{g(t);let{layout:e,isClassicSplitMenu:a}=I.value;e==="classic"&&a&&W.emit("setSendClassicChildren",(n=>{let i={children:[]};if(!r.defaultActive){const s=v(p.value,n);r.defaultActive=s.path}return b(p.value).map((s,_)=>{s.path===r.defaultActive&&(s.k=_,i.item={...s},i.children=[{...s}],s.children&&(i.children=s.children))}),i})(t.path))}),(t,e)=>{const a=d("SvgIcon"),n=d("el-sub-menu"),i=d("el-menu-item"),s=d("el-menu"),_=d("el-scrollbar");return f(),y("div",A,[c(_,{onWheel:R(F,["prevent"]),ref_key:"elMenuHorizontalScrollRef",ref:h},{default:u(()=>[c(s,{router:"","default-active":o(r).defaultActive,ellipsis:!1,"background-color":"transparent",mode:"horizontal"},{default:u(()=>[(f(!0),y(x,null,te(o(B),l=>(f(),y(x,null,[l.children&&l.children.length>0?(f(),z(n,{index:l.path,key:l.path},{title:u(()=>[c(a,{name:l.meta.icon},null,8,["name"]),D("span",null,k(o(m).setMenuI18n(l)),1)]),default:u(()=>[c(o(O),{chil:l.children},null,8,["chil"])]),_:2},1032,["index"])):(f(),z(i,{index:l.path,key:l.path},ne({_:2},[!l.meta.isLink||l.meta.isLink&&l.meta.isIframe?{name:"title",fn:u(()=>[c(a,{name:l.meta.icon},null,8,["name"]),E(" "+k(o(m).setMenuI18n(l)),1)]),key:"0"}:{name:"title",fn:u(()=>[D("a",{class:"w100",onClick:R(re=>(G=>{m.handleOpenLink(G)})(l),["prevent"])},[c(a,{name:l.meta.icon},null,8,["name"]),E(" "+k(o(m).setMenuI18n(l)),1)],8,L)]),key:"1"}]),1032,["index"]))],64))),256))]),_:1},8,["default-active"])]),_:1},512)])}}}),[["__scopeId","data-v-0b74e9d4"]])});export{ie as __tla,S as default};
