const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css"])))=>i.map(i=>d[i]);
var Ma=Object.defineProperty;var za=(G,D,j)=>D in G?Ma(G,D,{enumerable:!0,configurable:!0,writable:!0,value:j}):G[D]=j;var Y=(G,D,j)=>za(G,typeof D!="symbol"?D+"":D,j);import{d as Pa,k as T,w as ca,S as da,B as O,a as W,b as I,F as ce,t as n,v as d,D as X,f as a,E as z,G as L,u as F,e as le,p as ua,g as ie,q as de,x as ue}from"./vue.CnN__PXn.js";import{U as Va,W as Na,E as v,X as Aa,Y as ma,$ as pa,a1 as Fa,a as Ra,aj as ya,a3 as Wa,ak as Da,al as ha,D as Oa,P as Ga,am as ja,an as va,ao as qa,a9 as Ua,ap as Ba,a8 as Ya,q as Za,__tla as Ha}from"./index.BSP3cg_z.js";import Xa,{__tla as Ja}from"./TaskConfigDialog.DliaCU3T.js";import Ka,{__tla as Qa}from"./LogViewDialog.Bv4eI9Ym.js";let fa,et=Promise.all([(()=>{try{return Ha}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return Qa}catch{}})()]).then(async()=>{class G{constructor(t,c,i,p,y){Y(this,"map");Y(this,"mapContainer");Y(this,"mapLoading");Y(this,"mousePosition");Y(this,"mousePositionVisible");Y(this,"layersChangeCallback",null);this.map=t,this.mapContainer=c,this.mapLoading=i,this.mousePosition=p,this.mousePositionVisible=y}initMap(){if(!this.map.value&&this.mapContainer.value)try{this.map.value=new Va({target:this.mapContainer.value,view:new Na({center:[108,22.5],zoom:8,projection:"EPSG:4326"}),controls:[]}),this.map.value.on("pointermove",t=>{const c=t.coordinate;if(c){const[i,p]=c;this.mousePosition.value=`\u7ECF\u5EA6: ${i.toFixed(6)}, \u7EAC\u5EA6: ${p.toFixed(6)}`,this.mousePositionVisible.value=!0}}),this.map.value.getViewport().addEventListener("mouseleave",()=>{this.mousePositionVisible.value=!1})}catch{v.error("\u5730\u56FE\u521D\u59CB\u5316\u5931\u8D25")}}loadWMTSLayer(t){if(this.map.value&&t)try{this.mapLoading.value=!0;const c=t.split(":");if(c.length!==2)throw new Error(`\u56FE\u5C42ID\u683C\u5F0F\u4E0D\u6B63\u786E: ${t}`);const i=c[0],p=c[1],y="http://**************:8085/geoserver",g="EPSG:4326",u="image/png",m="",k=Aa(g);if(!k)throw new Error(`\u65E0\u6CD5\u83B7\u53D6\u6295\u5F71\u7CFB\u7EDF: ${g}`);const C=new ma({url:`${y}/gwc/service/wmts`,layer:t,matrixSet:g,format:u,projection:k,style:m,requestEncoding:"KVP",tileGrid:this.createWmtsTileGrid(k,g),wrapX:!0,transition:0,crossOrigin:"anonymous"});C.on("tileloaderror",K=>{}),C.on("tileloadend",()=>{});const P=new pa({preload:1/0,source:C,visible:!0,opacity:1,zIndex:0});P.layerId=`${i}:${p}`,P.workspace=i,P.layerName=p,P.displayName=p,P.layerType="WMTS",this.map.value.addLayer(P),this.triggerLayersChange(),this.fetchLayerExtentAndZoom(i,p)}catch{v.error("\u56FE\u5C42\u52A0\u8F7D\u5931\u8D25"),this.mapLoading.value=!1}}createWmtsTileGrid(t,c){const i=t.getExtent();let p,y,g;if(c==="EPSG:4326"){p=[-180,90],y=[],g=[];for(let u=0;u<22;u++)y[u]=.703125/Math.pow(2,u),g[u]=`EPSG:4326:${u}`}else{const u=Math.sqrt(i[2]-i[0])/256;y=new Array(19),g=new Array(19);for(let m=0;m<19;m++)y[m]=u/Math.pow(2,m),g[m]=m.toString();p=[i[0],i[3]]}return new Fa({origin:p,resolutions:y,matrixIds:g})}async fetchLayerExtentAndZoom(t,c){var i;try{const{getLayerBbox:p}=await Ra(async()=>{const{getLayerBbox:g}=await import("./index.BSP3cg_z.js").then(async u=>(await u.__tla,u)).then(u=>u.bT);return{getLayerBbox:g}},__vite__mapDeps([0,1,2])),y=await p(t,c);if(!(y&&y.status==="success"&&y.bbox&&y.bbox.latLon))throw new Error("API\u8FD4\u56DE\u7684\u8FB9\u754C\u6846\u6570\u636E\u683C\u5F0F\u4E0D\u6B63\u786E");{const{minx:g,miny:u,maxx:m,maxy:k}=y.bbox.latLon;if(g===-180&&u===-90&&m===180&&k===90)return void this.zoomToDefaultExtent();const C=[g,u,m,k];(i=this.map.value)==null||i.getView().fit(C,{padding:[50,50,50,50],duration:0})}}catch{this.zoomToDefaultExtent()}finally{this.mapLoading.value=!1}}zoomToDefaultExtent(){if(this.map.value)try{const t=[106,20,110,25];this.map.value.getView().fit(t,{padding:[50,50,50,50],duration:0})}catch{}}loadWMSLayer(t){if(this.map.value)try{if(this.mapLoading.value=!0,!t.workspace||!t.layerName)throw new Error("WMS\u56FE\u5C42\u914D\u7F6E\u7F3A\u5C11workspace\u6216layerName");const c="http://**************:8085/geoserver/wms",i={LAYERS:`${t.workspace}:${t.layerName}`,TILED:!0,FORMAT:"image/png",TRANSPARENT:!0,VERSION:"1.1.1",SRS:"EPSG:4326"};if(t.defaultStyle)if(t.defaultStyle==="Random"){const k=this.getRandomStyle();i.STYLES=k}else i.STYLES=t.defaultStyle;else{const k=this.getRandomStyle();i.STYLES=k}const p=new ya({url:c,params:i,serverType:"geoserver",crossOrigin:"anonymous",transition:0});let y=0,g=0,u=0;p.on("tileloadstart",k=>{u++}),p.on("tileloadend",k=>{y++,y===1&&(this.mapLoading.value=!1)}),p.on("tileloaderror",k=>{g++,g+y>=u&&u>0&&(this.mapLoading.value=!1)});const m=new pa({source:p,visible:!0,opacity:t.opacity!==void 0?t.opacity:.7,zIndex:t.zIndex||2});m.layerId=t.id,m.workspace=t.workspace,m.layerName=t.layerName,m.displayName=t.name,m.layerType="WMS",this.map.value.addLayer(m),this.triggerLayersChange(),setTimeout(()=>{this.mapLoading.value&&(this.mapLoading.value=!1)},1e4),setTimeout(()=>{this.mapLoading.value=!1},3e3)}catch{v.error("WMS\u56FE\u5C42\u52A0\u8F7D\u5931\u8D25"),this.mapLoading.value=!1}}getRandomStyle(){const t=["Blue","Brown","Cyan","Gold","Gray","Green","Lime","Magenta","Navy","Olive","Orange","Pink","Purple","Red","Teal","White","Yellow","Coral","Tomato"];return t[Math.floor(Math.random()*t.length)]}stopLoading(){this.mapLoading.value=!1}removeLayer(t){if(!this.map.value)return;const c=this.map.value.getLayers().getArray().find(i=>i.layerId===t);c&&(this.map.value.removeLayer(c),this.triggerLayersChange())}removeLayers(t){t.forEach(c=>{this.removeLayer(c)})}getLayersInfo(){return this.map.value?this.map.value.getLayers().getArray().map((t,c)=>{const i=t.getSource(),p=t.getZIndex(),y=t.getVisible(),g=t.getOpacity();let u="Unknown",m="",k="";if(i instanceof ma){if(u="WMTS",t.displayName)m=t.displayName;else if(t.layerName)m=t.layerName;else{const C=i.getLayer()||"WMTS\u56FE\u5C42";m=C.includes(":")?C.split(":")[1]:C}k=t.layerId||`wmts_${c}`}else if(i instanceof ya){if(u="WMS",t.displayName)m=t.displayName;else if(t.layerName)m=t.layerName;else{const C=i.getParams().LAYERS||"WMS\u56FE\u5C42";m=C.includes(":")?C.split(":")[1]:C}k=t.layerId||`wms_${c}`}return{id:k,name:m,type:u,visible:y,opacity:g,zIndex:p,layer:t}}).sort((t,c)=>(c.zIndex||0)-(t.zIndex||0)):[]}listLayers(){this.getLayersInfo().forEach((t,c)=>{})}toggleLayerVisibility(t){const c=this.getLayersInfo().find(i=>i.id===t);if(c){const i=!c.visible;c.layer.setVisible(i),this.triggerLayersChange()}}setLayerZIndex(t,c){const i=this.getLayersInfo().find(p=>p.id===t);i&&i.layer.setZIndex(c)}updateLayersOrder(t){t.forEach((c,i)=>{const p=t.length-i;this.setLayerZIndex(c,p)}),this.triggerLayersChange()}setLayersChangeListener(t){this.layersChangeCallback=t}triggerLayersChange(){this.layersChangeCallback&&this.layersChangeCallback()}resizeMap(){this.map.value&&this.map.value.updateSize()}cleanupMap(){this.map.value&&(this.map.value.setTarget(void 0),this.map.value=null)}}let D,j,me,pe,ye,he,ve,fe,ge,_e,we,ke,be,Le,Ee,xe,Se,$e,Ce,Te,Ie,Me,ze,Pe,Ve,Ne,Ae,Fe,Re,We,De,Oe,Ge,je,qe,Ue,Be,Ye,Ze,He,Xe,Je,Ke,Qe,ea,aa;D={key:0,class:"detail-content"},j={class:"basic-info"},me={class:"detail-layout"},pe={class:"left-section"},ye={class:"analysis-tasks"},he={class:"task-header"},ve={class:"header-actions"},fe={class:"task-list"},ge={key:1,class:"task-items"},_e={class:"task-info"},we={class:"task-header-row"},ke={class:"task-name-with-status"},be={class:"task-name"},Le={key:0,class:"task-statistics"},Ee={class:"stats-row"},xe={class:"stats-value"},Se={class:"stats-value"},$e={class:"stats-row"},Ce={class:"stats-value"},Te={class:"stats-value"},Ie={class:"stats-value"},Me={class:"stats-row"},ze={class:"stats-value"},Pe={class:"stats-value"},Ve={class:"task-model-row"},Ne={class:"model-info"},Ae={class:"model-value"},Fe={class:"model-info"},Re={class:"model-value model-name-truncated"},We={class:"task-actions"},De={class:"right-section"},Oe={class:"map-display"},Ge={class:"map-container"},je={key:0,class:"loading-overlay"},qe=["id"],Ue={class:"fullscreen-control"},Be={key:1,class:"mouse-position"},Ye={key:2,class:"map-legend"},Ze={class:"panel-content"},He={class:"layer-list"},Xe={class:"layer-items"},Je={class:"move-controls"},Ke={class:"layer-info"},Qe={class:"layer-name"},ea={class:"visibility-control"},aa={class:"dialog-footer"},fa=Za(Pa({__name:"AnalysisDetailDialog",props:{modelValue:{type:Boolean,default:!1},task:{default:null}},emits:["update:modelValue"],setup(ta,{expose:t,emit:c}){const i=ta,p=c,y=T(i.modelValue),g=T(!1),u=T(null),m=T(null),k=T(""),C=T(!1),P=T([]);ca(()=>i.modelValue,l=>{y.value=l,l&&i.task&&(re(),setTimeout(()=>{var e;E.initMap(),E.setLayersChangeListener(()=>{La()}),(e=i.task)!=null&&e.layer_name&&E.loadWMTSLayer(i.task.layer_name)},1e3))}),ca(()=>y.value,l=>{p("update:modelValue",l),!l&&m.value&&E.cleanupMap()});const K=T({}),ne=T(!1),oe=T(!1),sa=T(""),q=T(!1),Z=T([]),E=new G(m,u,g,k,C),re=async()=>{var l;if((l=i.task)!=null&&l.id)try{const e=`http://**************:8091/api/analysis/taskinfo/?id=${i.task.id}`,h=await fetch(e);if(!h.ok)throw new Error(`HTTP error! status: ${h.status}`);const x=await h.json();if(x.status!=="success"||!x.data)throw new Error(x.message||"API\u8FD4\u56DE\u72B6\u6001\u4E0D\u6B63\u786E");{const o=x.data.map(r=>{var V,s,N,A;const S={arableLand:"\u8015\u5730\u5206\u6790",constructionLand:"\u5EFA\u8BBE\u7528\u5730\u5206\u6790"}[r.analysis_category]||"\u5206\u6790",$=new Date(1e3*r.timestamp),_=$.getFullYear().toString()+($.getMonth()+1).toString().padStart(2,"0")+$.getDate().toString().padStart(2,"0")+$.getHours().toString().padStart(2,"0")+$.getMinutes().toString().padStart(2,"0");return{task_id:r.task_id,name:`${S}_${_}`,type:S,status:r.status,createTime:r.datetime,analysis_category:r.analysis_category,timestamp:r.timestamp,model_type:(V=r.parameters)==null?void 0:V.model_type,model_name:(s=r.parameters)==null?void 0:s.model_name,old_data_path:(N=r.input_files)==null?void 0:N.old_data_path,spatial_statistics:(A=r.results)==null?void 0:A.spatial_statistics,output_files:r.output_files,log_file:r.log_file}});P.value=o.reverse()}}catch{v.error("\u83B7\u53D6\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5")}},ga=()=>{re()},_a=async()=>{await(async()=>{try{const l="http://**************:8091/api/analysis/weight-info/",e=await fetch(l);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const h=await e.json();if(h.status!=="success")throw new Error(h.message||"API\u8FD4\u56DE\u72B6\u6001\u4E0D\u6B63\u786E");K.value=h.data}catch{v.error("\u83B7\u53D6\u6743\u91CD\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5")}})(),Object.keys(K.value).length!==0?ne.value=!0:v.error("\u65E0\u6CD5\u83B7\u53D6\u4EFB\u52A1\u914D\u7F6E\u4FE1\u606F")},wa=async l=>{try{v.info("\u6B63\u5728\u63D0\u4EA4\u5206\u6790\u4EFB\u52A1...");const e=await(async h=>{var x;try{const o="http://**************:8091",r=h.config;if(!r)throw new Error("\u914D\u7F6E\u5BF9\u8C61\u4E0D\u5B58\u5728");if(!r.model)throw new Error("\u6A21\u578B\u8DEF\u5F84\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5\u6A21\u578B\u9009\u62E9");const S={id:r.id||((x=i.task)==null?void 0:x.id)||"",image:r.image,model:r.model,old_data_path:r.old_data_path,area_threshold:r.area_threshold.toString()};Object.entries(S).forEach(([V,s])=>{});const $=`${o}/api/analysis/queued-combined-ai-spatial-analysis/?${new URLSearchParams(S).toString()}`,_=await fetch($,{method:"GET",headers:{"Content-Type":"application/json"}});if(!_.ok){let V="";try{const s=await _.text();V=s?` - ${s}`:""}catch{}throw new Error(`API\u8BF7\u6C42\u5931\u8D25: ${_.status} ${_.statusText}${V}`)}return await _.json()}catch(o){throw o}})(l);if(e.success!==!0)throw new Error(e.message||"API\u8FD4\u56DE\u72B6\u6001\u5F02\u5E38");{const h=e.message||"\u5206\u6790\u4EFB\u52A1\u5DF2\u6210\u529F\u63D0\u4EA4",x=e.data;x&&x.status==="\u7B49\u5F85\u4E2D"?v.success(`${h}\uFF0C\u4EFB\u52A1\u5DF2\u8FDB\u5165\u961F\u5217\uFF08\u4F4D\u7F6E\uFF1A${x.queue_position||"\u672A\u77E5"}\uFF09`):v.success(h),await re()}}catch(e){v.error(`\u63D0\u4EA4\u5206\u6790\u4EFB\u52A1\u5931\u8D25: ${e instanceof Error?e.message:"\u672A\u77E5\u9519\u8BEF"}`)}},la=l=>{E&&E.loadWMSLayer(l)},R=T(null),Q=T([]),ka=async l=>{if(R.value!==l.task_id){R.value&&ee();try{v.info("\u6B63\u5728\u83B7\u53D6\u4EFB\u52A1\u8BE6\u7EC6\u4FE1\u606F...");const e=await(async o=>{var r;try{const S=`http://**************:8091/api/analysis/taskinfo/?id=${(r=i.task)==null?void 0:r.id}`,$=await fetch(S,{method:"GET",headers:{"Content-Type":"application/json"}});if(!$.ok)throw new Error(`API\u8BF7\u6C42\u5931\u8D25: ${$.status} ${$.statusText}`);const _=await $.json();if(_.status==="success"&&_.data&&Array.isArray(_.data))return _.data.find(V=>V.task_id===o);throw new Error(_.message||"\u83B7\u53D6\u4EFB\u52A1\u8BE6\u7EC6\u4FE1\u606F\u5931\u8D25")}catch(S){throw S}})(l.task_id);if(!e)return void v.warning("\u672A\u627E\u5230\u8BE5\u4EFB\u52A1\u7684\u8BE6\u7EC6\u4FE1\u606F");const h=(o=>{var $,_,V,s,N;const r=[],S=o.analysis_category;if((_=($=o.geoserver_publish)==null?void 0:$.ai_result)!=null&&_.success&&o.geoserver_publish.ai_result.layer_name&&r.push({defaultStyle:"fenxi",id:`${S}:${o.geoserver_publish.ai_result.layer_name}`,name:"AI\u5206\u6790\u7ED3\u679C",type:"raster",protocol:"WMS",workspace:S,layerName:o.geoserver_publish.ai_result.layer_name,opacity:1,zIndex:3}),(s=(V=o.geoserver_publish)==null?void 0:V.final_result)!=null&&s.success&&o.geoserver_publish.final_result.layer_name&&r.push({defaultStyle:"flowtype",id:`${S}:${o.geoserver_publish.final_result.layer_name}`,name:"\u6D41\u5165\u6D41\u51FA\u7ED3\u679C",type:"raster",protocol:"WMS",workspace:S,layerName:o.geoserver_publish.final_result.layer_name,opacity:1,zIndex:4}),(N=o.input_files)==null?void 0:N.old_data_path){const A=o.input_files.old_data_path.split(/[/\\]/),w=A[A.length-1].replace(/\.[^/.]+$/,"");r.push({defaultStyle:"ditu",id:`${S}:${w}`,name:w,type:"raster",protocol:"WMS",workspace:S,layerName:w,opacity:1,zIndex:2})}return r})(e);if(h.length===0)return void v.warning("\u8BE5\u4EFB\u52A1\u6CA1\u6709\u53EF\u67E5\u770B\u7684\u56FE\u5C42\u6570\u636E");h.sort((o,r)=>o.zIndex-r.zIndex);const x=[];h.forEach(o=>{la(o),x.push(o.id)}),R.value=l.task_id,Q.value=x,v.success(`\u5DF2\u52A0\u8F7D ${h.length} \u4E2A\u5206\u6790\u7ED3\u679C\u56FE\u5C42`)}catch(e){v.error(`\u67E5\u770B\u5206\u6790\u4EFB\u52A1\u5931\u8D25: ${e instanceof Error?e.message:"\u672A\u77E5\u9519\u8BEF"}`)}}else ee()},ee=()=>{R.value&&Q.value.length!==0&&(E&&E.removeLayers(Q.value),R.value=null,Q.value=[],v.info("\u5DF2\u79FB\u9664\u5206\u6790\u7ED3\u679C\u56FE\u5C42"))},ba=()=>{q.value=!q.value},La=()=>{E&&(Z.value=E.getLayersInfo())},ia=T(!1),Ea=()=>{const l=document.querySelector(".map-container");l?ia.value?document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen():l.requestFullscreen?l.requestFullscreen():l.webkitRequestFullscreen?l.webkitRequestFullscreen():l.msRequestFullscreen&&l.msRequestFullscreen():v.error("\u5730\u56FE\u5BB9\u5668\u672A\u627E\u5230")},H=()=>{ia.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.msFullscreenElement),E&&setTimeout(()=>{E.resizeMap()},100)};document.addEventListener("fullscreenchange",H),document.addEventListener("webkitfullscreenchange",H),document.addEventListener("msfullscreenchange",H),da(()=>{document.removeEventListener("fullscreenchange",H),document.removeEventListener("webkitfullscreenchange",H),document.removeEventListener("msfullscreenchange",H)});const xa=l=>{switch(l){case"\u5B8C\u6210":case"\u5DF2\u5B8C\u6210":return"success";case"\u8FDB\u884C\u4E2D":return"warning";case"\u5F85\u5F00\u59CB":default:return"info";case"\u5931\u8D25":return"danger"}},Sa=l=>{switch(l){case"\u5B8C\u6210":case"\u5DF2\u5B8C\u6210":return"status-success";case"\u8FDB\u884C\u4E2D":return"status-processing";case"\u5F85\u5F00\u59CB":default:return"status-info";case"\u5931\u8D25":return"status-error"}},na=()=>{y.value=!1},$a=()=>{},oa=l=>l==null||isNaN(l)?"0.00":l.toFixed(2),Ca=(l,e=20)=>l?l.length<=e?l:l.substring(0,e)+"...":"-",Ta=l=>{if(!l)return"-";try{const{minx:e,miny:h,maxx:x,maxy:o}=l;return e!==void 0&&h!==void 0&&x!==void 0&&o!==void 0?`${e.toFixed(6)}, ${h.toFixed(6)}, ${x.toFixed(6)}, ${o.toFixed(6)}`:"-"}catch{return"-"}},Ia=l=>{if(!l)return"-";try{return l.split(/[/\\]/).pop()||"-"}catch{return"-"}};return da(()=>{E.cleanupMap()}),t({loadWMSLayer:la}),(l,e)=>{const h=O("el-descriptions-item"),x=O("el-descriptions"),o=O("el-button"),r=O("el-icon"),S=O("el-empty"),$=O("el-tag"),_=O("el-tooltip"),V=O("el-dialog");return I(),W(ce,null,[n(V,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=s=>y.value=s),title:"\u5206\u6790\u8BE6\u60C5",width:"80%","close-on-click-modal":!1,onClose:na},{footer:d(()=>[a("div",aa,[n(o,{onClick:na},{default:d(()=>e[21]||(e[21]=[z("\u5173\u95ED")])),_:1}),n(o,{type:"primary",onClick:$a,disabled:""},{default:d(()=>e[22]||(e[22]=[z("\u5BFC\u51FA\u62A5\u544A")])),_:1})])]),default:d(()=>[l.task?(I(),W("div",D,[a("div",j,[n(x,{column:3,border:""},{default:d(()=>[n(h,{label:"\u4EFB\u52A1ID"},{default:d(()=>[z(L(l.task.id),1)]),_:1}),n(h,{label:"\u5B8C\u6210\u65F6\u95F4"},{default:d(()=>{return[z(L((s=l.task.endTime,s?new Date(s).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-")),1)];var s}),_:1}),n(h,{label:"\u56FE\u5C42\u540D\u79F0"},{default:d(()=>[z(L(l.task.layer_name),1)]),_:1}),n(h,{label:"\u4E3B\u9898"},{default:d(()=>[z(L(l.task.theme),1)]),_:1}),n(h,{label:"\u5206\u6790\u6570"},{default:d(()=>[z(L(P.value.length),1)]),_:1}),n(h,{label:"\u7A7A\u95F4\u8303\u56F4"},{default:d(()=>[z(L(Ta(l.task.bbox)),1)]),_:1})]),_:1})]),a("div",me,[a("div",pe,[a("div",ye,[a("div",he,[e[5]||(e[5]=a("h4",null,"\u5206\u6790\u4EFB\u52A1",-1)),a("div",ve,[n(o,{type:"success",size:"small",icon:F(Wa),onClick:ga},{default:d(()=>e[3]||(e[3]=[z(" \u5237\u65B0 ")])),_:1},8,["icon"]),n(o,{type:"primary",size:"small",icon:F(Da),onClick:_a},{default:d(()=>e[4]||(e[4]=[z(" \u6DFB\u52A0\u4EFB\u52A1 ")])),_:1},8,["icon"])])]),a("div",fe,[P.value.length===0?(I(),le(S,{key:0,description:"\u6682\u65E0\u5206\u6790\u4EFB\u52A1"},{image:d(()=>[n(r,{size:"60",color:"#c0c4cc"},{default:d(()=>[n(F(ha))]),_:1})]),description:d(()=>e[6]||(e[6]=[a("p",null,"\u6682\u65E0\u5206\u6790\u4EFB\u52A1",-1),a("p",null,"\u70B9\u51FB\u4E0A\u65B9\u6309\u94AE\u6DFB\u52A0\u65B0\u4EFB\u52A1",-1)])),_:1})):(I(),W("div",ge,[(I(!0),W(ce,null,ua(P.value,(s,N)=>{return I(),W("div",{key:s.task_id,class:"task-item"},[a("div",_e,[a("div",we,[a("div",ke,[a("span",be,L(s.name),1),n($,{type:xa(s.status),class:ie(Sa(s.status)),size:"small"},{default:d(()=>[z(L(s.status),1)]),_:2},1032,["type","class"]),s.log_file?(I(),le(_,{key:0,content:"\u67E5\u770B\u65E5\u5FD7",placement:"top"},{default:d(()=>[n(o,{size:"small",circle:"",type:"info",icon:F(Oa),onClick:w=>{return f=s,sa.value=f.task_id,void(oe.value=!0);var f}},null,8,["icon","onClick"])]),_:2},1024)):X("",!0)])]),s.spatial_statistics?(I(),W("div",Le,[a("div",Ee,[e[7]||(e[7]=a("span",{class:"stats-label"},"\u5206\u6790\u7C7B\u578B:",-1)),a("span",xe,L((A=s.analysis_category,{arableLand:"\u8015\u5730",constructionLand:"\u5EFA\u8BBE\u7528\u5730"}[A]||A)),1),e[8]||(e[8]=a("span",{class:"stats-label"},"\u5BF9\u6BD4\u6570\u636E:",-1)),a("span",Se,L(Ia(s.old_data_path)),1)]),a("div",$e,[e[9]||(e[9]=a("span",{class:"stats-label"},"\u6D41\u51FA\u56FE\u6591\u6570:",-1)),a("span",Ce,L(s.spatial_statistics.outflow_count||0),1),e[10]||(e[10]=a("span",{class:"stats-label"},"\u6D41\u5165\u56FE\u6591\u6570:",-1)),a("span",Te,L(s.spatial_statistics.inflow_count||0),1),e[11]||(e[11]=a("span",{class:"stats-label"},"\u603B\u8BA1\u56FE\u6591\u6570:",-1)),a("span",Ie,L(s.spatial_statistics.total_count||0),1)]),a("div",Me,[e[12]||(e[12]=a("span",{class:"stats-label"},"\u6D41\u51FA\u9762\u79EF:",-1)),a("span",ze,L(oa(s.spatial_statistics.outflow_area))+"\u33A1",1),e[13]||(e[13]=a("span",{class:"stats-label"},"\u6D41\u5165\u9762\u79EF:",-1)),a("span",Pe,L(oa(s.spatial_statistics.inflow_area))+"\u33A1",1)])])):X("",!0),a("div",Ve,[a("div",Ne,[e[14]||(e[14]=a("span",{class:"model-label"},"\u6A21\u578B\u7C7B\u578B:",-1)),a("span",Ae,L(s.model_type||"-"),1)]),a("div",Fe,[e[15]||(e[15]=a("span",{class:"model-label"},"\u6A21\u578B\u540D:",-1)),n(_,{content:s.model_name||"-",placement:"top",disabled:!s.model_name||s.model_name.length<=20},{default:d(()=>[a("span",Re,L(Ca(s.model_name)),1)]),_:2},1032,["content","disabled"])])])]),a("div",We,[n(_,{content:R.value===s.task_id?"\u53D6\u6D88\u67E5\u770B\u5206\u6790\u7ED3\u679C":s.status==="\u5B8C\u6210"?"\u67E5\u770B\u5206\u6790\u7ED3\u679C":"\u4EFB\u52A1\u672A\u5B8C\u6210\uFF0C\u65E0\u6CD5\u67E5\u770B\u7ED3\u679C",placement:"top"},{default:d(()=>[n(o,{size:"small",type:R.value===s.task_id?"warning":"primary",onClick:w=>R.value===s.task_id?ee():ka(s),disabled:s.status!=="\u5B8C\u6210"},{default:d(()=>[z(L(R.value===s.task_id?"\u53D6\u6D88":"\u67E5\u770B"),1)]),_:2},1032,["type","onClick","disabled"])]),_:2},1032,["content"]),n(_,{content:s.status==="\u5B8C\u6210"?"\u4E0B\u8F7D\u5206\u6790\u7ED3\u679C":"\u4EFB\u52A1\u672A\u5B8C\u6210\uFF0C\u65E0\u6CD5\u4E0B\u8F7D\u7ED3\u679C",placement:"top"},{default:d(()=>[n(o,{size:"small",type:"success",onClick:w=>(async f=>{try{if(!f.output_files||!f.output_files.ai_output_path&&!f.output_files.final_output_path)return void v.error("\u8BE5\u4EFB\u52A1\u6CA1\u6709\u53EF\u4E0B\u8F7D\u7684\u6587\u4EF6");const M=[];if(f.output_files.ai_output_path&&M.push(f.output_files.ai_output_path),f.output_files.final_output_path&&M.push(f.output_files.final_output_path),M.length===0)return void v.error("\u6CA1\u6709\u627E\u5230\u53EF\u4E0B\u8F7D\u7684\u6587\u4EF6\u8DEF\u5F84");v.info("\u6B63\u5728\u51C6\u5907\u4E0B\u8F7D\u6587\u4EF6...");const b="http://**************:8091/api/analysis/download-data/",U=await fetch(b,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_paths:M})});if(!U.ok)throw new Error(`\u4E0B\u8F7D\u8BF7\u6C42\u5931\u8D25: ${U.status} ${U.statusText}`);const ae=await U.blob(),te=window.URL.createObjectURL(ae),B=document.createElement("a");B.href=te;const J=`analysis_data_${f.task_id}_${new Date().getTime()}.zip`;B.download=J,document.body.appendChild(B),B.click(),document.body.removeChild(B),window.URL.revokeObjectURL(te),v.success("\u6587\u4EF6\u4E0B\u8F7D\u6210\u529F")}catch(M){v.error(`\u4E0B\u8F7D\u5931\u8D25: ${M instanceof Error?M.message:"\u672A\u77E5\u9519\u8BEF"}`)}})(s),disabled:s.status!=="\u5B8C\u6210"},{default:d(()=>e[16]||(e[16]=[z(" \u4E0B\u8F7D ")])),_:2},1032,["onClick","disabled"])]),_:2},1032,["content"]),n(_,{content:"\u5220\u9664\u5206\u6790\u4EFB\u52A1",placement:"top"},{default:d(()=>[n(o,{size:"small",type:"danger",onClick:w=>(async f=>{var M;try{const b=P.value[f];if(!b)return void v.error("\u4EFB\u52A1\u4E0D\u5B58\u5728");let U=`\u786E\u5B9A\u8981\u5220\u9664\u5206\u6790\u4EFB\u52A1 "${b.name}" \u5417\uFF1F

\u5220\u9664\u540E\u5C06\u65E0\u6CD5\u6062\u590D\u4EFB\u52A1\u6570\u636E\u548C\u5206\u6790\u7ED3\u679C\u3002`,ae="\u786E\u8BA4\u5220\u9664\u4EFB\u52A1";b.status==="\u8FDB\u884C\u4E2D"&&(U=`\u5206\u6790\u4EFB\u52A1 "${b.name}" \u6B63\u5728\u8FDB\u884C\u4E2D\uFF0C\u786E\u5B9A\u8981\u5F3A\u5236\u5220\u9664\u5417\uFF1F

\u5220\u9664\u540E\u4EFB\u52A1\u5C06\u88AB\u4E2D\u6B62\uFF0C\u5DF2\u4EA7\u751F\u7684\u6570\u636E\u548C\u5206\u6790\u7ED3\u679C\u5C06\u65E0\u6CD5\u6062\u590D\u3002`,ae="\u786E\u8BA4\u5F3A\u5236\u5220\u9664\u8FDB\u884C\u4E2D\u7684\u4EFB\u52A1"),await Ya.confirm(U,ae,{confirmButtonText:b.status==="\u8FDB\u884C\u4E2D"?"\u5F3A\u5236\u5220\u9664":"\u786E\u5B9A\u5220\u9664",cancelButtonText:"\u53D6\u6D88",type:"warning",dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!0});const te=b.status==="\u8FDB\u884C\u4E2D"?"\u6B63\u5728\u4E2D\u6B62\u5E76\u5220\u9664\u4EFB\u52A1...":"\u6B63\u5728\u5220\u9664\u4EFB\u52A1...";if(v.info(te),!((M=i.task)!=null&&M.id))return void v.error("\u65E0\u6CD5\u83B7\u53D6\u4E3B\u4EFB\u52A1ID\uFF0C\u5220\u9664\u5931\u8D25");const B=`http://**************:8091/api/analysis/delete-task/?id=${i.task.id}&task_id=${b.task_id}`,J=await fetch(B,{method:"GET"});let se;try{se=await J.json()}catch{throw new Error(`\u5220\u9664\u8BF7\u6C42\u5931\u8D25: ${J.status} ${J.statusText}`)}if(se.success!==!0){const ra=se.message||"\u5220\u9664\u5931\u8D25\uFF0C\u670D\u52A1\u5668\u8FD4\u56DE\u5F02\u5E38\u72B6\u6001";throw new Error(ra)}P.value.splice(f,1),v.success(se.message||"\u4EFB\u52A1\u5220\u9664\u6210\u529F"),R.value===b.task_id&&ee()}catch(b){if(b==="cancel"||b instanceof Error&&b.message==="cancel")return;v.error(`\u5220\u9664\u4EFB\u52A1\u5931\u8D25: ${b instanceof Error?b.message:"\u672A\u77E5\u9519\u8BEF"}`)}})(N)},{default:d(()=>e[17]||(e[17]=[z(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:2},1024)])]);var A}),128))]))])])]),a("div",De,[a("div",Oe,[e[20]||(e[20]=a("h4",null,"\u5730\u56FE\u5C55\u793A",-1)),a("div",Ge,[g.value?(I(),W("div",je,[n(r,{class:"loading-icon"},{default:d(()=>[n(F(Ga))]),_:1}),e[18]||(e[18]=a("p",null,"\u6B63\u5728\u52A0\u8F7D\u56FE\u5C42...",-1))])):X("",!0),a("div",{id:`analysis-map-${Date.now()}`,ref_key:"mapContainer",ref:u,class:"map-content"},null,8,qe),a("div",Ue,[n(_,{content:"\u5168\u5C4F\u663E\u793A",placement:"left"},{default:d(()=>[n(o,{size:"small",circle:"",type:"primary",icon:F(ja),onClick:Ea},null,8,["icon"])]),_:1})]),C.value?(I(),W("div",Be,L(k.value),1)):X("",!0),R.value?(I(),W("div",Ye,e[19]||(e[19]=[a("div",{class:"legend-title"},"\u56FE\u4F8B",-1),a("div",{class:"legend-items"},[a("div",{class:"legend-column"},[a("div",{class:"legend-item"},[a("div",{class:"legend-color legend-inflow"}),a("span",{class:"legend-text"},"\u6D41\u5165")]),a("div",{class:"legend-item"},[a("div",{class:"legend-color legend-outflow"}),a("span",{class:"legend-text"},"\u6D41\u51FA")])]),a("div",{class:"legend-column"},[a("div",{class:"legend-item"},[a("div",{class:"legend-color legend-ai-result"}),a("span",{class:"legend-text"},"AI\u5206\u6790\u7ED3\u679C")]),a("div",{class:"legend-item"},[a("div",{class:"legend-color legend-compare-data"}),a("span",{class:"legend-text"},"\u5BF9\u6BD4\u6570\u636E")])])],-1)]))):X("",!0),a("div",{class:ie(["layer-control-panel",{collapsed:!q.value}])},[a("div",{class:"panel-header",onClick:ba},[n(r,null,{default:d(()=>[n(F(ha))]),_:1}),de(a("span",null,"\u56FE\u5C42\u63A7\u5236",512),[[ue,q.value]]),de(n(r,{class:ie(["collapse-icon",{collapsed:!q.value}])},{default:d(()=>[n(F(va))]),_:1},8,["class"]),[[ue,q.value]])]),de(a("div",Ze,[a("div",He,[a("div",Xe,[(I(!0),W(ce,null,ua(Z.value,(s,N)=>(I(),W("div",{key:s.id,class:ie(["layer-item",{"layer-hidden":!s.visible}])},[a("div",Je,[n(o,{size:"small",disabled:N===0,onClick:A=>(w=>{if(w>0&&E){const f=[...Z.value];[f[w],f[w-1]]=[f[w-1],f[w]];const M=f.map(b=>b.id);E.updateLayersOrder(M)}})(N),class:"move-btn move-up"},{default:d(()=>[n(r,null,{default:d(()=>[n(F(qa))]),_:1})]),_:2},1032,["disabled","onClick"]),n(o,{size:"small",disabled:N===Z.value.length-1,onClick:A=>(w=>{if(w<Z.value.length-1&&E){const f=[...Z.value];[f[w],f[w+1]]=[f[w+1],f[w]];const M=f.map(b=>b.id);E.updateLayersOrder(M)}})(N),class:"move-btn move-down"},{default:d(()=>[n(r,null,{default:d(()=>[n(F(va))]),_:1})]),_:2},1032,["disabled","onClick"])]),a("div",Ke,[a("div",Qe,L(s.name),1)]),a("div",ea,[n(o,{size:"small",type:s.visible?"primary":"info",onClick:A=>{return w=s.id,void(E&&E.toggleLayerVisibility(w));var w},class:"visibility-btn"},{default:d(()=>[n(r,null,{default:d(()=>[s.visible?(I(),le(F(Ua),{key:0})):(I(),le(F(Ba),{key:1}))]),_:2},1024)]),_:2},1032,["type","onClick"])])],2))),128))])])],512),[[ue,q.value]])],2)])])])])])):X("",!0)]),_:1},8,["modelValue"]),n(Xa,{modelValue:ne.value,"onUpdate:modelValue":e[1]||(e[1]=s=>ne.value=s),task:l.task,"weight-info":K.value,onConfirm:wa},null,8,["modelValue","task","weight-info"]),n(Ka,{modelValue:oe.value,"onUpdate:modelValue":e[2]||(e[2]=s=>oe.value=s),"task-id":sa.value},null,8,["modelValue","task-id"])],64)}}}),[["__scopeId","data-v-3d514611"]])});export{et as __tla,fa as default};
