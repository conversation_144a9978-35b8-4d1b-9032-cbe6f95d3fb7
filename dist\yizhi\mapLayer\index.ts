/**
 * @file index.ts
 * @description 地图图层管理模块的入口文件，导出所有相关功能
 * 
 * 该文件作为地图图层管理模块的统一入口，导出了：
 * - 图层管理器Store（useMapLayerManagerStore）
 * - 图层相关类型定义（MapLayer、LayerStyle、StyleRule）
 * - 样式工具函数（createPointStyle、createLineStyle等）
 * 
 * 通过这个入口文件，应用可以方便地导入所需的图层管理功能，
 * 而不需要直接引用模块内部的具体文件。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
/*
 * @Description: 地图图层管理入口文件
 */
import { useMapLayerManagerStore } from './mapLayerManager';
import { MapLayer, LayerStyle, StyleRule } from './types';
import {
	createPointStyle,
	createLineStyle,
	createPolygonStyle,
	createIconStyle,
	parseColor,
	evaluateFilter,
	createStyleByGeometryType
} from './styleUtils';

// 导出类型和工具函数
export type { MapLayer, LayerStyle, StyleRule };
export {
	useMapLayerManagerStore,
	createPointStyle,
	createLineStyle,
	createPolygonStyle,
	createIconStyle,
	parseColor,
	evaluateFilter,
	createStyleByGeometryType
}; 