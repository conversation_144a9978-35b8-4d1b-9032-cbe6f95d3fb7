import{q as I,w as x,__tla as B}from"./index.BSP3cg_z.js";import{d as h,B as e,a as S,b as s,t as i,f as m,v as n,G as q,z as D,s as E,e as u,u as w,F as M,p as j}from"./vue.CnN__PXn.js";let z,G=Promise.all([(()=>{try{return B}catch{}})()]).then(async()=>{let v,g,_,y,F,b;v={class:"container"},g={class:"container-span"},_=h({name:"shortcut"}),y=I(Object.assign(_,{props:{icon:{type:String,default:()=>"menu-outlined",required:!1},label:{type:String,default:()=>"\u5FEB\u6377\u65B9\u5F0F",required:!1},color:{type:String,default:()=>"",required:!1}},setup(k){const t=k,r=["#7265E6","#FFBF00","#00A2AE","#F56A00","#1890FF","#606D80"],c=(a,d)=>{switch(arguments.length){case 1:return parseInt(Math.random()*a+1,10);case 2:return parseInt(Math.random()*(d-a+1)+a,10);default:return 0}};return(a,d)=>{const p=e("SvgIcon"),f=e("el-tag");return s(),S("div",v,[i(f,{color:t.color?t.color:r[c(0,r.length-1)],class:"container-tag"},{default:n(()=>[i(p,{name:t.icon,size:25,color:"#ffffff"},null,8,["name"])]),_:1},8,["color"]),m("span",g,q(a.$t(t.label)),1)])}}}),[["__scopeId","data-v-eb6ef064"]]),F={class:"card-header"},b=h({name:"SysFavoriteDashboard"}),z=I(h({...b,setup(k){const t=D(),r=x(),{favoriteRoutes:c}=E(r);return(a,d)=>{const p=e("SvgIcon"),f=e("el-col"),A=e("el-row"),R=e("el-empty"),T=e("el-card");return s(),u(T,{class:"box-card",style:{height:"100%"}},{header:n(()=>[m("div",F,[m("span",null,q(a.$t("home.quickNavigationToolsTip")),1)])]),default:n(()=>[w(c).length>0?(s(),u(A,{key:0,gutter:10},{default:n(()=>[(s(!0),S(M,null,j(w(c),l=>(s(),u(f,{class:"shortcutCard",span:6,key:l.id},{default:n(()=>{var C;return[i(p,{name:"ele-Close",size:12,class:"shortcutCardClose",onClick:$=>{return o=l,void r.delFavoriteRoutes(o);var o}},null,8,["onClick"]),i(y,{icon:(C=l.meta)==null?void 0:C.icon,label:l.name,onClick:$=>{return o=l.path,void t.push(o);var o}},null,8,["icon","label","onClick"])]}),_:2},1024))),128))]),_:1})):(s(),u(R,{key:1,description:a.$t("home.addFavoriteRoutesTip")},null,8,["description"]))]),_:1})}}}),[["__scopeId","data-v-a99fda71"]])});export{G as __tla,z as default};
