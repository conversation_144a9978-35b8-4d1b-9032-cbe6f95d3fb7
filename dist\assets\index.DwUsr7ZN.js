import{d as c,c as t,a as r,b as l,e as $,r as g,n as i,f as v,g as z}from"./vue.CnN__PXn.js";const x=["src"],k=["href"],w=c({name:"svgIcon"}),b=c({...w,props:{name:{type:String},size:{type:Number,default:()=>14},color:{type:String}},setup(h){const e=h,p=["https","http","/src","/assets","data:image","/"],o=t(()=>e==null?void 0:e.name),d=t(()=>{var s;return(s=e==null?void 0:e.name)==null?void 0:s.startsWith("ele-")}),m=t(()=>p.find(s=>{var a;return(a=e.name)==null?void 0:a.startsWith(s)})),y=t(()=>{var s;return(s=e==null?void 0:e.name)==null?void 0:s.startsWith("local-")}),n=t(()=>`font-size: ${e.size}px;color: ${e.color};`),u=t(()=>`width: ${e.size}px;height: ${e.size}px;display: inline-block;overflow: hidden;`),f=t(()=>{const s=[];return["-webkit","-ms","-o","-moz"].forEach(a=>s.push(`${a}-filter: drop-shadow(${e.color} 30px 0);`)),`width: ${e.size}px;height: ${e.size}px;position: relative;left: -${e.size}px;${s.join("")}`});return(s,a)=>d.value?(l(),r("i",{key:0,class:"el-icon",style:i(n.value)},[(l(),$(g(o.value)))],4)):m.value?(l(),r("div",{key:1,style:i(u.value)},[v("img",{src:o.value,style:i(f.value)},null,12,x)],4)):y.value?(l(),r("svg",{key:2,class:"svg-icon icon",style:i(u.value)},[v("use",{href:`#${o.value}`},null,8,k)],4)):(l(),r("i",{key:3,class:z(o.value),style:i(n.value)},null,6))}});export{b as default};
