const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css"])))=>i.map(i=>d[i]);
import{l as _s}from"./pigx-app.DmHLWGl6.js";import{d as be,k as S,$ as _e,c as he,o as we,S as Ee,B as R,a as F,b as f,f as r,t as d,v as u,E as se,G as Y,n as De,F as ve,p as Fe,e as ae,D as H,u as M,Q as ke,Z as ja,a0 as Ce,r as Ha,g as le,A as Wa,w as Ka,y as Qa,z as Cs}from"./vue.CnN__PXn.js";import{as as bs,at as ws,au as ks,av as xs,aw as Ps,ax as Ls,ay as Ts,az as Vs,aA as Ss,aB as Is,aC as Ms,aD as Es,aE as Ds,aF as Fs,aG as As,aH as Rs,aI as Os,aJ as zs,aK as Ns,aL as qe,aM as Us,aN as Xa,aO as Za,aP as $s,aQ as Ja,aR as Gs,aS as qs,aT as Bs,aU as pe,aV as js,aW as Ya,aX as es,aY as xe,aZ as Be,a_ as Pe,a$ as ts,b0 as Hs,b1 as as,b2 as ss,b3 as ls,b4 as Ws,b5 as Ks,b6 as Qs,b7 as Ae,b8 as Xs,b9 as Zs,ba as Js,bb as Ys,bc as ns,bd as el,be as tl,bf as is,bg as je,E as b,bh as al,a as sl,q as He,bi as ll,D as nl,bj as il,bk as os,bl as ol,bm as rl,a2 as rs,bn as dl,bo as cl,a5 as ul,bp as hl,bq as re,br as pl,bs as ml,bt as me,bu as gl,bv as vl,a8 as yl,bw as ds,bx as cs,by as us,bz as Re,bA as We,M as Ke,O as fl,bB as _l,bC as Cl,bD as bl,a6 as wl,bE as kl,bF as xl,bG as Pl,bH as Ll,bI as hs,bJ as Tl,bK as Vl,__tla as Sl}from"./index.BSP3cg_z.js";let ps,Il=Promise.all([(()=>{try{return Sl}catch{}})()]).then(async()=>{class Le extends bs{constructor(e,a,s){super(),s!==void 0&&a===void 0?this.setFlatCoordinates(s,e):(a=a||0,this.setCenterAndRadius(e,a,s))}clone(){const e=new Le(this.flatCoordinates.slice(),void 0,this.layout);return e.applyProperties(this),e}closestPointXY(e,a,s,l){const i=this.flatCoordinates,o=e-i[0],t=a-i[1],c=o*o+t*t;if(c<l){if(c===0)for(let C=0;C<this.stride;++C)s[C]=i[C];else{const C=this.getRadius()/Math.sqrt(c);s[0]=i[0]+C*o,s[1]=i[1]+C*t;for(let x=2;x<this.stride;++x)s[x]=i[x]}return s.length=this.stride,c}return l}containsXY(e,a){const s=this.flatCoordinates,l=e-s[0],i=a-s[1];return l*l+i*i<=this.getRadiusSquared_()}getCenter(){return this.flatCoordinates.slice(0,this.stride)}computeExtent(e){const a=this.flatCoordinates,s=a[this.stride]-a[0];return ws(a[0]-s,a[1]-s,a[0]+s,a[1]+s,e)}getRadius(){return Math.sqrt(this.getRadiusSquared_())}getRadiusSquared_(){const e=this.flatCoordinates[this.stride]-this.flatCoordinates[0],a=this.flatCoordinates[this.stride+1]-this.flatCoordinates[1];return e*e+a*a}getType(){return"Circle"}intersectsExtent(e){const a=this.getExtent();if(ks(e,a)){const s=this.getCenter();return e[0]<=s[0]&&e[2]>=s[0]||e[1]<=s[1]&&e[3]>=s[1]||xs(e,this.intersectsCoordinate.bind(this))}return!1}setCenter(e){const a=this.stride,s=this.flatCoordinates[a]-this.flatCoordinates[0],l=e.slice();l[a]=l[0]+s;for(let i=1;i<a;++i)l[a+i]=e[i];this.setFlatCoordinates(this.layout,l),this.changed()}setCenterAndRadius(e,a,s){this.setLayout(s,e,0),this.flatCoordinates||(this.flatCoordinates=[]);const l=this.flatCoordinates;let i=Ps(l,0,e,this.stride);l[i++]=l[0]+a;for(let o=1,t=this.stride;o<t;++o)l[i++]=l[o];l.length=i,this.changed()}getCoordinates(){return null}setCoordinates(e,a){}setRadius(e){this.flatCoordinates[this.stride]=this.flatCoordinates[0]+e,this.changed()}rotate(e,a){const s=this.getCenter(),l=this.getStride();this.setCenter(Ls(s,0,s.length,l,e,a,s)),this.changed()}}Le.prototype.transform;const Oe="element",ze="map",Ne="offset",Te="position",Ue="positioning";class ms extends Ts{constructor(e){super(),this.on,this.once,this.un,this.options=e,this.id=e.id,this.insertFirst=e.insertFirst===void 0||e.insertFirst,this.stopEvent=e.stopEvent===void 0||e.stopEvent,this.element=document.createElement("div"),this.element.className=e.className!==void 0?e.className:"ol-overlay-container "+Vs,this.element.style.position="absolute",this.element.style.pointerEvents="auto",this.autoPan=e.autoPan===!0?{}:e.autoPan||void 0,this.rendered={transform_:"",visible:!0},this.mapPostrenderListenerKey=null,this.addChangeListener(Oe,this.handleElementChanged),this.addChangeListener(ze,this.handleMapChanged),this.addChangeListener(Ne,this.handleOffsetChanged),this.addChangeListener(Te,this.handlePositionChanged),this.addChangeListener(Ue,this.handlePositioningChanged),e.element!==void 0&&this.setElement(e.element),this.setOffset(e.offset!==void 0?e.offset:[0,0]),this.setPositioning(e.positioning||"top-left"),e.position!==void 0&&this.setPosition(e.position)}getElement(){return this.get(Oe)}getId(){return this.id}getMap(){return this.get(ze)||null}getOffset(){return this.get(Ne)}getPosition(){return this.get(Te)}getPositioning(){return this.get(Ue)}handleElementChanged(){Ss(this.element);const e=this.getElement();e&&this.element.appendChild(e)}handleMapChanged(){this.mapPostrenderListenerKey&&(Is(this.element),Ms(this.mapPostrenderListenerKey),this.mapPostrenderListenerKey=null);const e=this.getMap();if(e){this.mapPostrenderListenerKey=Es(e,Ds.POSTRENDER,this.render,this),this.updatePixelPosition();const a=this.stopEvent?e.getOverlayContainerStopEvent():e.getOverlayContainer();this.insertFirst?a.insertBefore(this.element,a.childNodes[0]||null):a.appendChild(this.element),this.performAutoPan()}}render(){this.updatePixelPosition()}handleOffsetChanged(){this.updatePixelPosition()}handlePositionChanged(){this.updatePixelPosition(),this.performAutoPan()}handlePositioningChanged(){this.updatePixelPosition()}setElement(e){this.set(Oe,e)}setMap(e){this.set(ze,e)}setOffset(e){this.set(Ne,e)}setPosition(e){this.set(Te,e)}performAutoPan(){this.autoPan&&this.panIntoView(this.autoPan)}panIntoView(e){const a=this.getMap();if(!a||!a.getTargetElement()||!this.get(Te))return;const s=this.getRect(a.getTargetElement(),a.getSize()),l=this.getElement(),i=this.getRect(l,[Fs(l),As(l)]),o=(e=e||{}).margin===void 0?20:e.margin;if(!Rs(s,i)){const t=i[0]-s[0],c=s[2]-i[2],C=i[1]-s[1],x=s[3]-i[3],T=[0,0];if(t<0?T[0]=t-o:c<0&&(T[0]=Math.abs(c)+o),C<0?T[1]=C-o:x<0&&(T[1]=Math.abs(x)+o),T[0]!==0||T[1]!==0){const $=a.getView().getCenterInternal(),U=a.getPixelFromCoordinateInternal($);if(!U)return;const W=[U[0]+T[0],U[1]+T[1]],Z=e.animation||{};a.getView().animateInternal({center:a.getCoordinateFromPixelInternal(W),duration:Z.duration,easing:Z.easing})}}}getRect(e,a){const s=e.getBoundingClientRect(),l=s.left+window.pageXOffset,i=s.top+window.pageYOffset;return[l,i,l+a[0],i+a[1]]}setPositioning(e){this.set(Ue,e)}setVisible(e){this.rendered.visible!==e&&(this.element.style.display=e?"":"none",this.rendered.visible=e)}updatePixelPosition(){const e=this.getMap(),a=this.getPosition();if(!e||!e.isRendered()||!a)return void this.setVisible(!1);const s=e.getPixelFromCoordinate(a),l=e.getSize();this.updateRenderedPosition(s,l)}updateRenderedPosition(e,a){const s=this.element.style,l=this.getOffset(),i=this.getPositioning();this.setVisible(!0);let o="0%",t="0%";i=="bottom-right"||i=="center-right"||i=="top-right"?o="-100%":i!="bottom-center"&&i!="center-center"&&i!="top-center"||(o="-50%"),i=="bottom-left"||i=="bottom-center"||i=="bottom-right"?t="-100%":i!="center-left"&&i!="center-center"&&i!="center-right"||(t="-50%");const c=`translate(${o}, ${t}) translate(${Math.round(e[0]+l[0])+"px"}, ${Math.round(e[1]+l[1])+"px"})`;this.rendered.transform_!=c&&(this.rendered.transform_=c,s.transform=c)}getOptions(){return this.options}}const Qe="drawstart",gs="drawend",vs="drawabort";class Ve extends Js{constructor(e,a){super(e),this.feature=a}}function Se(E,e){return ns(E[0],E[1],e[0],e[1])}function ye(E,e){const a=E.length;return e<0?E[e+a]:e>=a?E[e-a]:E[e]}function Ie(E,e,a){let s,l;e<a?(s=e,l=a):(s=a,l=e);const i=Math.ceil(s),o=Math.floor(l);if(i>o)return Se(fe(E,s),fe(E,l));let t=0;s<i&&(t+=Se(fe(E,s),ye(E,i))),o<l&&(t+=Se(ye(E,o),fe(E,l)));for(let c=i;c<o-1;++c)t+=Se(ye(E,c),ye(E,c+1));return t}function Xe(E,e,a){if(e instanceof Pe)Me(E,e.getCoordinates(),!1,a);else if(e instanceof as){const s=e.getCoordinates();for(let l=0,i=s.length;l<i;++l)Me(E,s[l],!1,a)}else if(e instanceof Ae){const s=e.getCoordinates();for(let l=0,i=s.length;l<i;++l)Me(E,s[l],!0,a)}else if(e instanceof ss){const s=e.getCoordinates();for(let l=0,i=s.length;l<i;++l){const o=s[l];for(let t=0,c=o.length;t<c;++t)Me(E,o[t],!0,a)}}else if(e instanceof Ys){const s=e.getGeometries();for(let l=0;l<s.length;++l)Xe(E,s[l],a)}}const $e={index:-1,endIndex:NaN};function Me(E,e,a,s){const l=E[0],i=E[1];for(let o=0,t=e.length-1;o<t;++o){const c=Ze(l,i,e[o],e[o+1]);if(c.squaredDistance===0){const C=o+c.along;return void s.push({coordinates:e,ring:a,startIndex:C,endIndex:C})}}}const Ge={along:0,squaredDistance:0};function Ze(E,e,a,s){const l=a[0],i=a[1],o=s[0]-l,t=s[1]-i;let c=0,C=l,x=i;return o===0&&t===0||(c=tl(((E-l)*o+(e-i)*t)/(o*o+t*t),0,1),C+=o*c,x+=t*c),Ge.along=c,Ge.squaredDistance=el(ns(E,e,C,x),10),Ge}function fe(E,e){const a=E.length;let s=Math.floor(e);const l=e-s;s>=a?s-=a:s<0&&(s+=a);let i=s+1;i>=a&&(i-=a);const o=E[s],t=o[0],c=o[1],C=E[i];return[t+(C[0]-t)*l,c+(C[1]-c)*l]}class ys extends Os{constructor(e){const a=e;a.stopDown||(a.stopDown=zs),super(a),this.on,this.once,this.un,this.shouldHandle_=!1,this.downPx_=null,this.downTimeout_,this.lastDragTime_,this.pointerType_,this.freehand_=!1,this.source_=e.source?e.source:null,this.features_=e.features?e.features:null,this.snapTolerance_=e.snapTolerance?e.snapTolerance:12,this.type_=e.type,this.mode_=function(l){switch(l){case"Point":case"MultiPoint":return"Point";case"LineString":case"MultiLineString":return"LineString";case"Polygon":case"MultiPolygon":return"Polygon";case"Circle":return"Circle";default:throw new Error("Invalid type: "+l)}}(this.type_),this.stopClick_=!!e.stopClick,this.minPoints_=e.minPoints?e.minPoints:this.mode_==="Polygon"?3:2,this.maxPoints_=this.mode_==="Circle"?2:e.maxPoints?e.maxPoints:1/0,this.finishCondition_=e.finishCondition?e.finishCondition:Ns,this.geometryLayout_=e.geometryLayout?e.geometryLayout:"XY";let s=e.geometryFunction;if(!s){const l=this.mode_;if(l==="Circle")s=function(i,o,t){const c=o||new Le([NaN,NaN]),C=qe(i[0]),x=Us(C,qe(i[i.length-1]));return c.setCenterAndRadius(C,Math.sqrt(x),this.geometryLayout_),c};else{let i;l==="Point"?i=Be:l==="LineString"?i=Pe:l==="Polygon"&&(i=Ae),s=function(o,t,c){return t?l==="Polygon"?o[0].length?t.setCoordinates([o[0].concat([o[0][0]])],this.geometryLayout_):t.setCoordinates([],this.geometryLayout_):t.setCoordinates(o,this.geometryLayout_):t=new i(o,this.geometryLayout_),t}}}this.geometryFunction_=s,this.dragVertexDelay_=e.dragVertexDelay!==void 0?e.dragVertexDelay:500,this.finishCoordinate_=null,this.sketchFeature_=null,this.sketchPoint_=null,this.sketchCoords_=null,this.sketchLine_=null,this.sketchLineCoords_=null,this.squaredClickTolerance_=e.clickTolerance?e.clickTolerance*e.clickTolerance:36,this.overlay_=new Xa({source:new Za({useSpatialIndex:!1,wrapX:!!e.wrapX&&e.wrapX}),style:e.style?e.style:fs(),updateWhileInteracting:!0}),this.geometryName_=e.geometryName,this.condition_=e.condition?e.condition:$s,this.freehandCondition_,e.freehand?this.freehandCondition_=Ja:this.freehandCondition_=e.freehandCondition?e.freehandCondition:Gs,this.traceCondition_,this.setTrace(e.trace||!1),this.traceState_={active:!1},this.traceSource_=e.traceSource||e.source||null,this.addChangeListener(qs.ACTIVE,this.updateState_)}setTrace(e){let a;a=e?e===!0?Ja:e:Xs,this.traceCondition_=a}setMap(e){super.setMap(e),this.updateState_()}getOverlay(){return this.overlay_}handleEvent(e){e.originalEvent.type===Bs.CONTEXTMENU&&e.originalEvent.preventDefault(),this.freehand_=this.mode_!=="Point"&&this.freehandCondition_(e);let a=e.type===pe.POINTERMOVE,s=!0;return!this.freehand_&&this.lastDragTime_&&e.type===pe.POINTERDRAG&&(Date.now()-this.lastDragTime_>=this.dragVertexDelay_?(this.downPx_=e.pixel,this.shouldHandle_=!this.freehand_,a=!0):this.lastDragTime_=void 0,this.shouldHandle_&&this.downTimeout_!==void 0&&(clearTimeout(this.downTimeout_),this.downTimeout_=void 0)),this.freehand_&&e.type===pe.POINTERDRAG&&this.sketchFeature_!==null?(this.addToDrawing_(e.coordinate),s=!1):this.freehand_&&e.type===pe.POINTERDOWN?s=!1:a&&this.getPointerCount()<2?(s=e.type===pe.POINTERMOVE,s&&this.freehand_?(this.handlePointerMove_(e),this.shouldHandle_&&e.originalEvent.preventDefault()):(e.originalEvent.pointerType==="mouse"||e.type===pe.POINTERDRAG&&this.downTimeout_===void 0)&&this.handlePointerMove_(e)):e.type===pe.DBLCLICK&&(s=!1),super.handleEvent(e)&&s}handleDownEvent(e){return this.shouldHandle_=!this.freehand_,this.freehand_?(this.downPx_=e.pixel,this.finishCoordinate_||this.startDrawing_(e.coordinate),!0):this.condition_(e)?(this.lastDragTime_=Date.now(),this.downTimeout_=setTimeout(()=>{this.handlePointerMove_(new js(pe.POINTERMOVE,e.map,e.originalEvent,!1,e.frameState))},this.dragVertexDelay_),this.downPx_=e.pixel,!0):(this.lastDragTime_=void 0,!1)}deactivateTrace_(){this.traceState_={active:!1}}toggleTraceState_(e){if(!this.traceSource_||!this.traceCondition_(e))return;if(this.traceState_.active)return void this.deactivateTrace_();const a=this.getMap(),s=a.getCoordinateFromPixel([e.pixel[0]-this.snapTolerance_,e.pixel[1]+this.snapTolerance_]),l=a.getCoordinateFromPixel([e.pixel[0]+this.snapTolerance_,e.pixel[1]-this.snapTolerance_]),i=Ya([s,l]),o=this.traceSource_.getFeaturesInExtent(i);if(o.length===0)return;const t=function(c,C){const x=[];for(let T=0;T<C.length;++T)Xe(c,C[T].getGeometry(),x);return x}(e.coordinate,o);t.length&&(this.traceState_={active:!0,startPx:e.pixel.slice(),targets:t,targetIndex:-1})}addOrRemoveTracedCoordinates_(e,a){const s=e.startIndex<=e.endIndex;s===e.startIndex<=a?s&&a>e.endIndex||!s&&a<e.endIndex?this.addTracedCoordinates_(e,e.endIndex,a):(s&&a<e.endIndex||!s&&a>e.endIndex)&&this.removeTracedCoordinates_(a,e.endIndex):(this.removeTracedCoordinates_(e.startIndex,e.endIndex),this.addTracedCoordinates_(e,e.startIndex,a))}removeTracedCoordinates_(e,a){if(e===a)return;let s=0;if(e<a){const l=Math.ceil(e);let i=Math.floor(a);i===a&&(i-=1),s=i-l+1}else{const l=Math.floor(e);let i=Math.ceil(a);i===a&&(i+=1),s=l-i+1}s>0&&this.removeLastPoints_(s)}addTracedCoordinates_(e,a,s){if(a===s)return;const l=[];if(a<s){const i=Math.ceil(a);let o=Math.floor(s);o===s&&(o-=1);for(let t=i;t<=o;++t)l.push(ye(e.coordinates,t))}else{const i=Math.floor(a);let o=Math.ceil(s);o===s&&(o+=1);for(let t=i;t>=o;--t)l.push(ye(e.coordinates,t))}l.length&&this.appendCoordinates(l)}updateTrace_(e){const a=this.traceState_;if(!a.active||a.targetIndex===-1&&es(a.startPx,e.pixel)<this.snapTolerance_)return;const s=function(t,c,C,x){const T=t[0],$=t[1];let U=1/0,W=-1,Z=NaN;for(let B=0;B<c.targets.length;++B){const V=c.targets[B],q=V.coordinates;let G,O=1/0;for(let w=0;w<q.length-1;++w){const h=Ze(T,$,q[w],q[w+1]);h.squaredDistance<O&&(O=h.squaredDistance,G=w+h.along)}O<U&&(U=O,V.ring&&c.targetIndex===B&&(V.endIndex>V.startIndex?G<V.startIndex&&(G+=q.length):V.endIndex<V.startIndex&&G>V.startIndex&&(G-=q.length)),Z=G,W=B)}const ie=c.targets[W];let ne=ie.ring;if(c.targetIndex===W&&ne){const B=fe(ie.coordinates,Z),V=C.getPixelFromCoordinate(B);es(V,c.startPx)>x&&(ne=!1)}if(ne){const B=ie.coordinates,V=B.length,q=ie.startIndex,G=Z;if(q<G){const O=Ie(B,q,G);Ie(B,q,G-V)<O&&(Z-=V)}else{const O=Ie(B,q,G);Ie(B,q,G+V)<O&&(Z+=V)}}return $e.index=W,$e.endIndex=Z,$e}(e.coordinate,a,this.getMap(),this.snapTolerance_);if(a.targetIndex!==s.index){if(a.targetIndex!==-1){const c=a.targets[a.targetIndex];this.removeTracedCoordinates_(c.startIndex,c.endIndex)}const t=a.targets[s.index];this.addTracedCoordinates_(t,t.startIndex,s.endIndex)}else{const t=a.targets[a.targetIndex];this.addOrRemoveTracedCoordinates_(t,s.endIndex)}a.targetIndex=s.index;const l=a.targets[a.targetIndex];l.endIndex=s.endIndex;const i=fe(l.coordinates,l.endIndex),o=this.getMap().getPixelFromCoordinate(i);e.coordinate=i,e.pixel=[Math.round(o[0]),Math.round(o[1])]}handleUpEvent(e){let a=!0;if(this.getPointerCount()===0){this.downTimeout_&&(clearTimeout(this.downTimeout_),this.downTimeout_=void 0),this.handlePointerMove_(e);const s=this.traceState_.active;if(this.toggleTraceState_(e),this.shouldHandle_){const l=!this.finishCoordinate_;l&&this.startDrawing_(e.coordinate),!l&&this.freehand_?this.finishDrawing():this.freehand_||l&&this.mode_!=="Point"||(this.atFinish_(e.pixel,s)?this.finishCondition_(e)&&this.finishDrawing():this.addToDrawing_(e.coordinate)),a=!1}else this.freehand_&&this.abortDrawing()}return!a&&this.stopClick_&&e.preventDefault(),a}handlePointerMove_(e){if(this.pointerType_=e.originalEvent.pointerType,this.downPx_&&(!this.freehand_&&this.shouldHandle_||this.freehand_&&!this.shouldHandle_)){const a=this.downPx_,s=e.pixel,l=a[0]-s[0],i=a[1]-s[1],o=l*l+i*i;if(this.shouldHandle_=this.freehand_?o>this.squaredClickTolerance_:o<=this.squaredClickTolerance_,!this.shouldHandle_)return}this.finishCoordinate_?(this.updateTrace_(e),this.modifyDrawing_(e.coordinate)):this.createOrUpdateSketchPoint_(e.coordinate.slice())}atFinish_(e,a){let s=!1;if(this.sketchFeature_){let l=!1,i=[this.finishCoordinate_];const o=this.mode_;if(o==="Point")s=!0;else if(o==="Circle")s=this.sketchCoords_.length===2;else if(o==="LineString")l=!a&&this.sketchCoords_.length>this.minPoints_;else if(o==="Polygon"){const t=this.sketchCoords_;l=t[0].length>this.minPoints_,i=[t[0][0],t[0][t[0].length-2]],i=a?[t[0][0]]:[t[0][0],t[0][t[0].length-2]]}if(l){const t=this.getMap();for(let c=0,C=i.length;c<C;c++){const x=i[c],T=t.getPixelFromCoordinate(x),$=e[0]-T[0],U=e[1]-T[1],W=this.freehand_?1:this.snapTolerance_;if(s=Math.sqrt($*$+U*U)<=W,s){this.finishCoordinate_=x;break}}}}return s}createOrUpdateSketchPoint_(e){this.sketchPoint_?this.sketchPoint_.getGeometry().setCoordinates(e):(this.sketchPoint_=new xe(new Be(e)),this.updateSketchFeatures_())}createOrUpdateCustomSketchLine_(e){this.sketchLine_||(this.sketchLine_=new xe);const a=e.getLinearRing(0);let s=this.sketchLine_.getGeometry();s?(s.setFlatCoordinates(a.getLayout(),a.getFlatCoordinates()),s.changed()):(s=new Pe(a.getFlatCoordinates(),a.getLayout()),this.sketchLine_.setGeometry(s))}startDrawing_(e){const a=this.getMap().getView().getProjection(),s=ts(this.geometryLayout_);for(;e.length<s;)e.push(0);this.finishCoordinate_=e,this.mode_==="Point"?this.sketchCoords_=e.slice():this.mode_==="Polygon"?(this.sketchCoords_=[[e.slice(),e.slice()]],this.sketchLineCoords_=this.sketchCoords_[0]):this.sketchCoords_=[e.slice(),e.slice()],this.sketchLineCoords_&&(this.sketchLine_=new xe(new Pe(this.sketchLineCoords_)));const l=this.geometryFunction_(this.sketchCoords_,void 0,a);this.sketchFeature_=new xe,this.geometryName_&&this.sketchFeature_.setGeometryName(this.geometryName_),this.sketchFeature_.setGeometry(l),this.updateSketchFeatures_(),this.dispatchEvent(new Ve(Qe,this.sketchFeature_))}modifyDrawing_(e){const a=this.getMap(),s=this.sketchFeature_.getGeometry(),l=a.getView().getProjection(),i=ts(this.geometryLayout_);let o,t;for(;e.length<i;)e.push(0);this.mode_==="Point"?t=this.sketchCoords_:this.mode_==="Polygon"?(o=this.sketchCoords_[0],t=o[o.length-1],this.atFinish_(a.getPixelFromCoordinate(e))&&(e=this.finishCoordinate_.slice())):(o=this.sketchCoords_,t=o[o.length-1]),t[0]=e[0],t[1]=e[1],this.geometryFunction_(this.sketchCoords_,s,l),this.sketchPoint_&&this.sketchPoint_.getGeometry().setCoordinates(e),s.getType()==="Polygon"&&this.mode_!=="Polygon"?this.createOrUpdateCustomSketchLine_(s):this.sketchLineCoords_&&this.sketchLine_.getGeometry().setCoordinates(this.sketchLineCoords_),this.updateSketchFeatures_()}addToDrawing_(e){const a=this.sketchFeature_.getGeometry(),s=this.getMap().getView().getProjection();let l,i;const o=this.mode_;o==="LineString"||o==="Circle"?(this.finishCoordinate_=e.slice(),i=this.sketchCoords_,i.length>=this.maxPoints_&&(this.freehand_?i.pop():l=!0),i.push(e.slice()),this.geometryFunction_(i,a,s)):o==="Polygon"&&(i=this.sketchCoords_[0],i.length>=this.maxPoints_&&(this.freehand_?i.pop():l=!0),i.push(e.slice()),l&&(this.finishCoordinate_=i[0]),this.geometryFunction_(this.sketchCoords_,a,s)),this.createOrUpdateSketchPoint_(e.slice()),this.updateSketchFeatures_(),l&&this.finishDrawing()}removeLastPoints_(e){if(!this.sketchFeature_)return;const a=this.sketchFeature_.getGeometry(),s=this.getMap().getView().getProjection(),l=this.mode_;for(let i=0;i<e;++i){let o;if(l==="LineString"||l==="Circle"){if(o=this.sketchCoords_,o.splice(-2,1),o.length>=2){this.finishCoordinate_=o[o.length-2].slice();const t=this.finishCoordinate_.slice();o[o.length-1]=t,this.createOrUpdateSketchPoint_(t)}this.geometryFunction_(o,a,s),a.getType()==="Polygon"&&this.sketchLine_&&this.createOrUpdateCustomSketchLine_(a)}else if(l==="Polygon"){o=this.sketchCoords_[0],o.splice(-2,1);const t=this.sketchLine_.getGeometry();if(o.length>=2){const c=o[o.length-2].slice();o[o.length-1]=c,this.createOrUpdateSketchPoint_(c)}t.setCoordinates(o),this.geometryFunction_(this.sketchCoords_,a,s)}if(o.length===1){this.abortDrawing();break}}this.updateSketchFeatures_()}removeLastPoint(){this.removeLastPoints_(1)}finishDrawing(){const e=this.abortDrawing_();if(!e)return;let a=this.sketchCoords_;const s=e.getGeometry(),l=this.getMap().getView().getProjection();this.mode_==="LineString"?(a.pop(),this.geometryFunction_(a,s,l)):this.mode_==="Polygon"&&(a[0].pop(),this.geometryFunction_(a,s,l),a=s.getCoordinates()),this.type_==="MultiPoint"?e.setGeometry(new Hs([a])):this.type_==="MultiLineString"?e.setGeometry(new as([a])):this.type_==="MultiPolygon"&&e.setGeometry(new ss([a])),this.dispatchEvent(new Ve(gs,e)),this.features_&&this.features_.push(e),this.source_&&this.source_.addFeature(e)}abortDrawing_(){this.finishCoordinate_=null;const e=this.sketchFeature_;return this.sketchFeature_=null,this.sketchPoint_=null,this.sketchLine_=null,this.overlay_.getSource().clear(!0),this.deactivateTrace_(),e}abortDrawing(){const e=this.abortDrawing_();e&&this.dispatchEvent(new Ve(vs,e))}appendCoordinates(e){const a=this.mode_,s=!this.sketchFeature_;let l;if(s&&this.startDrawing_(e[0]),a==="LineString"||a==="Circle")l=this.sketchCoords_;else{if(a!=="Polygon")return;l=this.sketchCoords_&&this.sketchCoords_.length?this.sketchCoords_[0]:[]}s&&l.shift(),l.pop();for(let o=0;o<e.length;o++)this.addToDrawing_(e[o]);const i=e[e.length-1];this.addToDrawing_(i),this.modifyDrawing_(i)}extend(e){const a=e.getGeometry();this.sketchFeature_=e,this.sketchCoords_=a.getCoordinates();const s=this.sketchCoords_[this.sketchCoords_.length-1];this.finishCoordinate_=s.slice(),this.sketchCoords_.push(s.slice()),this.sketchPoint_=new xe(new Be(s)),this.updateSketchFeatures_(),this.dispatchEvent(new Ve(Qe,this.sketchFeature_))}updateSketchFeatures_(){const e=[];this.sketchFeature_&&e.push(this.sketchFeature_),this.sketchLine_&&e.push(this.sketchLine_),this.sketchPoint_&&e.push(this.sketchPoint_);const a=this.overlay_.getSource();a.clear(!0),a.addFeatures(e)}updateState_(){const e=this.getMap(),a=this.getActive();e&&a||this.abortDrawing(),this.overlay_.setMap(a?e:null)}}function fs(){const E=Zs();return function(e,a){return E[e.getGeometry().getType()]}}let Je,Ye,et,tt,at,st,lt,nt,it,ot,rt,dt,ct,ut,ht,pt,mt,gt,vt,yt,ft,_t,Ct,bt,wt,kt,xt,Pt,Lt,Tt,Vt,St,It,Mt,Et,Dt,Ft,At,Rt,Ot,zt,Nt,Ut,$t,Gt,qt,Bt,jt,Ht,Wt,Kt,Qt,Xt,Zt,Jt,Yt,ea,ta,aa,sa,la,na,ia,oa,ra,da,ca,ua,ha,pa,ma,ga,va,ya,fa,_a,Ca,ba,wa,ka,xa,Pa,La,Ta,Va,Sa,Ia,Ma,Ea,Da,Fa,Aa,Ra,Oa,za,Na,Ua,$a,Ga,qa,Ba;Je={class:"hd-base-layer-control"},Ye={class:"panel-content"},et={class:"control-toolbar"},tt={class:"theme-filter"},at={class:"layer-list-container"},st={class:"group-title"},lt={class:"layer-items"},nt={class:"layer-header"},it={key:0,class:"layer-opacity-control"},ot={key:1,class:"layer-style-control"},rt={class:"style-item"},dt={class:"style-item"},ct={class:"style-item"},ut={key:0,class:"metadata-content"},ht={class:"metadata-item"},pt={class:"metadata-value"},mt={class:"metadata-item"},gt={class:"metadata-value"},vt={class:"metadata-item"},yt={class:"metadata-value"},ft={key:0,class:"metadata-item"},_t={class:"metadata-value"},Ct={key:1,class:"metadata-item"},bt={class:"metadata-value"},wt={key:2,class:"metadata-item"},kt={class:"metadata-value"},xt={key:3,class:"metadata-item"},Pt={class:"metadata-value"},Lt={key:4,class:"metadata-item"},Tt={class:"metadata-value"},Vt={key:5,class:"metadata-item"},St={class:"metadata-value"},It={class:"dialog-footer"},Mt=be({__name:"BaseLayerControl",setup(E){const e=S(""),a=S(""),s=S(!1),l=S(null),i=S(""),o=is();je();const t=S([]),c=_e("layerQuery",{toggleQueryMode:()=>{},isQueryModeActive:S(!1)}),C=he(()=>c.isQueryModeActive.value),x=he(()=>o.layerGroups);he(()=>o.themesList);const T=he(()=>x.value.filter(w=>a.value&&w.name!==a.value?!1:e.value?w.layers.some(h=>h.name.toLowerCase().includes(e.value.toLowerCase())):!0).sort((w,h)=>w.name.localeCompare(h.name,"zh-CN"))),$=w=>e.value?w.layers.filter(h=>h.name.toLowerCase().includes(e.value.toLowerCase())):w.layers,U=w=>{const h=$(w);return h.length!==0&&h.every(ee=>ee.visible)},W=w=>{const h=$(w);if(h.length===0)return!1;const ee=h.some(v=>v.visible),K=h.some(v=>!v.visible);return ee&&K},Z=async w=>{l.value=w,i.value="\u6B63\u5728\u83B7\u53D6...",s.value=!0;try{if(w.workspace&&w.layerName&&o.layerManager){const h=w.workspace,ee=w.layerName;try{const{getLayerBbox:K}=await sl(async()=>{const{getLayerBbox:n}=await import("./index.BSP3cg_z.js").then(async m=>(await m.__tla,m)).then(m=>m.bT);return{getLayerBbox:n}},__vite__mapDeps([0,1,2])),v=await K(h,ee);if(v&&v.status==="success"&&v.bbox){const{latLon:n}=v.bbox;if(n)return void(i.value=`\u7ECF\u5EA6: ${n.minx.toFixed(6)} ~ ${n.maxx.toFixed(6)}, \u7EAC\u5EA6: ${n.miny.toFixed(6)} ~ ${n.maxy.toFixed(6)}`)}}catch{}}if(o.layerManager){const h=o.layerManager.getLayer(w.id);if(h){const ee=h.getSource();if(ee&&ee.getExtent)try{const K=ee.getExtent();if(K&&K.length===4&&!K.some(v=>isNaN(v)))return void(i.value=`[${K[0].toFixed(2)}, ${K[1].toFixed(2)}, ${K[2].toFixed(2)}, ${K[3].toFixed(2)}]`)}catch{}}}i.value="\u672A\u77E5"}catch{i.value="\u83B7\u53D6\u5931\u8D25"}},ie=w=>{w.showStyleControl=!w.showStyleControl},ne=w=>{o.updateLayerStyle(w)},B=w=>{try{w.visible||(w.visible=!0,o.toggleLayerVisibility(w,!0)),setTimeout(()=>{if(o.layerManager){const h=w.id;o.layerManager.zoomToLayer(h),b.success(`\u5DF2\u5B9A\u4F4D\u5230\u56FE\u5C42: ${w.name}`)}else b.error("\u65E0\u6CD5\u5B9A\u4F4D\u56FE\u5C42\uFF0C\u56FE\u5C42\u7BA1\u7406\u5668\u5B9E\u4F8B\u4E0D\u5B58\u5728")},1e3)}catch(h){b.error(`\u7F29\u653E\u5230\u56FE\u5C42\u5931\u8D25: ${h.message||"\u672A\u77E5\u9519\u8BEF"}`)}},V=w=>{o.toggleLayerLabels(w)},q=w=>w.labelField!=null&&w.labelField!=="",G=()=>{e.value&&(t.value=T.value.map((w,h)=>h.toString()))},O=()=>{c.toggleQueryMode()};return we(async()=>{try{await o.loadMapConfig("baseMap3","baseStyle3"),t.value=[]}catch{b.error("\u52A0\u8F7D\u56FE\u5C42\u914D\u7F6E\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5")}}),Ee(()=>{C.value&&c.toggleQueryMode()}),(w,h)=>{const ee=R("el-input"),K=R("el-button"),v=R("el-checkbox"),n=R("el-icon"),m=R("el-dropdown-item"),g=R("el-dropdown-menu"),_=R("el-dropdown"),D=R("el-slider"),j=R("el-color-picker"),p=R("el-collapse-item"),k=R("el-collapse"),P=R("el-dialog");return f(),F("div",Je,[r("div",Ye,[r("div",et,[d(ee,{modelValue:e.value,"onUpdate:modelValue":h[0]||(h[0]=z=>e.value=z),placeholder:"\u641C\u7D22\u56FE\u5C42",clearable:"","prefix-icon":"Search",size:"small",onInput:G},null,8,["modelValue"]),r("div",tt,[d(K,{size:"small",class:"location-query-btn",onClick:O,style:De({backgroundColor:C.value?"rgba(24, 144, 255, 0.5)":"rgba(64, 158, 255, 0.5)",borderColor:C.value?"rgba(24, 144, 255, 0.8)":"rgba(64, 158, 255, 0.8)",color:"white",boxShadow:C.value?"0 0 10px rgba(24, 144, 255, 0.6)":"0 2px 4px rgba(0, 0, 0, 0.1)"})},{default:u(()=>[se(Y(C.value?"\u5173\u95ED\u70B9\u51FB\u4F4D\u7F6E\u67E5\u8BE2":"\u70B9\u51FB\u4F4D\u7F6E\u67E5\u8BE2"),1)]),_:1},8,["style"])])]),r("div",at,[d(k,{modelValue:t.value,"onUpdate:modelValue":h[2]||(h[2]=z=>t.value=z),class:"layer-group-list"},{default:u(()=>[(f(!0),F(ve,null,Fe(T.value,(z,Q)=>(f(),ae(p,{key:Q,name:Q.toString()},{title:u(()=>[r("div",st,[d(v,{modelValue:U(z),indeterminate:W(z),onChange:L=>((J,I)=>{o.toggleGroupVisibility(J,I)})(z,L),onClick:h[1]||(h[1]=ke(()=>{},["stop"]))},{default:u(()=>[se(Y(z.name),1)]),_:2},1032,["modelValue","indeterminate","onChange"])])]),default:u(()=>[r("div",lt,[(f(!0),F(ve,null,Fe($(z),(L,J)=>(f(),F("div",{key:J,class:"layer-item"},[r("div",nt,[d(v,{modelValue:L.visible,"onUpdate:modelValue":I=>L.visible=I,onChange:I=>((X,te)=>{o.toggleLayerVisibility(X,te)})(L,I)},{default:u(()=>[se(Y(L.name),1)]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),d(_,{trigger:"click",onCommand:I=>((X,te)=>{switch(X){case"metadata":Z(te);break;case"style":ie(te);break;case"zoom":B(te);break;case"label":V(te)}})(I,L)},{dropdown:u(()=>[d(g,null,{default:u(()=>[d(m,{command:"metadata"},{default:u(()=>h[5]||(h[5]=[se("\u56FE\u5C42\u4FE1\u606F")])),_:1}),L.type==="vector"?(f(),ae(m,{key:0,command:"style"},{default:u(()=>h[6]||(h[6]=[se("\u6837\u5F0F\u8BBE\u7F6E")])),_:1})):H("",!0),d(m,{command:"zoom"},{default:u(()=>h[7]||(h[7]=[se("\u5B9A\u4F4D\u56FE\u5C42")])),_:1}),L.type==="vector"&&q(L)?(f(),ae(m,{key:1,command:"label"},{default:u(()=>[se(Y(L.showLabels?"\u9690\u85CF\u6807\u6CE8":"\u663E\u793A\u6807\u6CE8"),1)]),_:2},1024)):H("",!0)]),_:2},1024)]),default:u(()=>[d(n,{class:"layer-actions-btn"},{default:u(()=>[d(M(al))]),_:1})]),_:2},1032,["onCommand"])]),L.showOpacityControl?(f(),F("div",it,[h[8]||(h[8]=r("span",{class:"opacity-label"},"\u900F\u660E\u5EA6:",-1)),d(D,{modelValue:L.opacity,"onUpdate:modelValue":I=>L.opacity=I,min:0,max:100,step:5,onChange:I=>((X,te)=>{o.setLayerOpacity(X,te)})(L,I)},null,8,["modelValue","onUpdate:modelValue","onChange"])])):H("",!0),L.showStyleControl&&L.type==="vector"?(f(),F("div",ot,[h[12]||(h[12]=r("div",{class:"style-title"},"\u6837\u5F0F\u8BBE\u7F6E",-1)),r("div",rt,[h[9]||(h[9]=r("span",null,"\u7EBF\u6761\u989C\u8272:",-1)),d(j,{modelValue:L.style.color,"onUpdate:modelValue":I=>L.style.color=I,size:"small","show-alpha":"",onChange:I=>ne(L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),r("div",dt,[h[10]||(h[10]=r("span",null,"\u7EBF\u6761\u5BBD\u5EA6:",-1)),d(D,{modelValue:L.style.weight,"onUpdate:modelValue":I=>L.style.weight=I,min:.5,max:5,step:.5,onChange:I=>ne(L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),r("div",ct,[h[11]||(h[11]=r("span",null,"\u586B\u5145\u989C\u8272:",-1)),d(j,{modelValue:L.style.fillColor,"onUpdate:modelValue":I=>L.style.fillColor=I,size:"small","show-alpha":"",onChange:I=>ne(L)},null,8,["modelValue","onUpdate:modelValue","onChange"])])])):H("",!0)]))),128))])]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])])]),d(P,{modelValue:s.value,"onUpdate:modelValue":h[4]||(h[4]=z=>s.value=z),title:"\u56FE\u5C42\u4FE1\u606F",width:"500px","destroy-on-close":"",center:"","append-to-body":""},{footer:u(()=>[r("span",It,[d(K,{onClick:h[3]||(h[3]=z=>s.value=!1)},{default:u(()=>h[22]||(h[22]=[se("\u5173\u95ED")])),_:1})])]),default:u(()=>[l.value?(f(),F("div",ut,[r("div",ht,[h[13]||(h[13]=r("span",{class:"metadata-label"},"\u56FE\u5C42\u540D\u79F0:",-1)),r("span",pt,Y(l.value.name),1)]),r("div",mt,[h[14]||(h[14]=r("span",{class:"metadata-label"},"\u56FE\u5C42ID:",-1)),r("span",gt,Y(l.value.id),1)]),r("div",vt,[h[15]||(h[15]=r("span",{class:"metadata-label"},"\u56FE\u5C42\u7C7B\u578B:",-1)),r("span",yt,Y(l.value.type==="vector"?"\u77E2\u91CF\u56FE\u5C42":"\u6805\u683C\u56FE\u5C42"),1)]),l.value.workspace?(f(),F("div",ft,[h[16]||(h[16]=r("span",{class:"metadata-label"},"\u5DE5\u4F5C\u7A7A\u95F4:",-1)),r("span",_t,Y(l.value.workspace),1)])):H("",!0),l.value.layerName?(f(),F("div",Ct,[h[17]||(h[17]=r("span",{class:"metadata-label"},"\u670D\u52A1\u56FE\u5C42\u540D:",-1)),r("span",bt,Y(l.value.layerName),1)])):H("",!0),l.value.protocol?(f(),F("div",wt,[h[18]||(h[18]=r("span",{class:"metadata-label"},"\u52A0\u8F7D\u534F\u8BAE:",-1)),r("span",kt,Y(l.value.protocol),1)])):H("",!0),l.value.theme?(f(),F("div",xt,[h[19]||(h[19]=r("span",{class:"metadata-label"},"\u4E3B\u9898\u5206\u7C7B:",-1)),r("span",Pt,Y(l.value.theme),1)])):H("",!0),l.value.geometryType?(f(),F("div",Lt,[h[20]||(h[20]=r("span",{class:"metadata-label"},"\u51E0\u4F55\u7C7B\u578B:",-1)),r("span",Tt,Y(l.value.geometryType),1)])):H("",!0),i.value?(f(),F("div",Vt,[h[21]||(h[21]=r("span",{class:"metadata-label"},"\u8FB9\u754C\u4FE1\u606F:",-1)),r("span",St,Y(i.value),1)])):H("",!0)])):H("",!0)]),_:1},8,["modelValue"])])}}}),Et=He(Mt,[["__scopeId","data-v-36a50752"]]),Dt={class:"hd-feature-attributes"},Ft={class:"panel-title"},At={class:"panel-content"},Rt=He(be({__name:"FeatureAttributesPanel",emits:["close"],setup(E,{emit:e}){const a=_e("featureAttributes",S({}));_e("attributeLayerId",S(""));const s=_e("attributeTitle",S("")),l=_e("gisTools",null),i=he(()=>a.value?Object.entries(a.value).filter(([t])=>t!=="layerId"&&t!=="geometry"&&t!=="the_geom").map(([t,c])=>({name:t,value:c===null?"":String(c)})):[]);_e("closeAttributesPanel",()=>{});const o=()=>{l&&typeof l.activateAttributeInfo=="function"&&l.activateAttributeInfo()};return(t,c)=>{const C=R("el-button"),x=R("el-empty"),T=R("el-table-column"),$=R("el-table"),U=R("el-scrollbar");return f(),F("div",Dt,[r("div",Ft,[r("span",null,Y(M(s)||"\u8981\u7D20\u5C5E\u6027\u4FE1\u606F"),1)]),r("div",At,[M(a)&&Object.keys(M(a)).length!==0?(f(),ae(U,{key:1},{default:u(()=>[d($,{data:i.value,stripe:"",style:{width:"100%"}},{default:u(()=>[d(T,{prop:"name",label:"\u5C5E\u6027\u540D\u79F0",width:"140","show-overflow-tooltip":""}),d(T,{prop:"value",label:"\u5C5E\u6027\u503C","show-overflow-tooltip":""})]),_:1},8,["data"])]),_:1})):(f(),ae(x,{key:0,description:"\u6682\u65E0\u8981\u7D20\u5C5E\u6027\u4FE1\u606F"},{description:u(()=>c[0]||(c[0]=[r("p",null,"\u8BF7\u4F7F\u7528\u5C5E\u6027\u67E5\u8BE2\u5DE5\u5177\u70B9\u51FB\u5730\u56FE\u8981\u7D20",-1)])),default:u(()=>[d(C,{type:"primary",onClick:o},{default:u(()=>c[1]||(c[1]=[se("\u6FC0\u6D3B\u5C5E\u6027\u67E5\u8BE2")])),_:1})]),_:1}))])])}}}),[["__scopeId","data-v-fc04e8c7"]]),Ot={class:"hd-layer-manager-container"},zt={class:"hd-layer-manager-content"},Nt={class:"hd-layer-panels"},Ut={class:"panel-content-area"},$t={key:1,class:"empty-panel"},Gt={class:"panel-selector"},qt=["onClick"],Bt={class:"panel-icon-wrapper"},jt={class:"panel-label"},Ht={__name:"index",props:{position:{type:String,default:"right"},visible:{type:Boolean,default:!0},gisToolsRef:{type:Object,default:null}},emits:["initialized"],setup(E,{expose:e,emit:a}){const s=[{name:"baseLayerControl",label:"\u56FE\u5C42\u63A7\u5236",component:ja(Et),icon:ll},{name:"featureAttributes",label:"\u8981\u7D20\u5C5E\u6027",component:ja(Rt),icon:nl}],l=S(""),i=S(null),o=is(),t=je(),c=S(!1),C=S(!1),x=S("idle"),T=S({count:0,layers:[],status:""}),$=S(""),U=S(""),W=S({longitude:0,latitude:0}),Z=S({}),ie=S(""),ne=S("");function B(){V("baseLayerControl"),Z.value={},ie.value="",ne.value=""}Ce("featureAttributes",Z),Ce("map",he(()=>{var n;return(n=t.map)==null?void 0:n.map})),Ce("attributeLayerId",ie),Ce("attributeTitle",ne),Ce("closeAttributesPanel",B),e({showAttributesPanel:function(n,m,g){Z.value=n,ie.value=m,ne.value=g,l.value!=="featureAttributes"&&V("featureAttributes"),v("attributes-panel-shown",{layerId:m})},closeAttributesPanel:B}),he(()=>T.value.layers?T.value.layers:[]);const V=n=>{if(l.value===n)l.value="",i.value=null;else{l.value=n;const m=s.find(g=>g.name===n);i.value=m?m.component:null}};function q(){var n,m,g,_;c.value=!c.value,c.value?((n=t.map)!=null&&n.mapContainer&&(t.map.mapContainer.style.cursor="crosshair"),(m=t.map)!=null&&m.map&&t.map.map.on("click",G),b({message:"\u67E5\u8BE2\u6A21\u5F0F\u5DF2\u6FC0\u6D3B\uFF0C\u70B9\u51FB\u5730\u56FE\u4F4D\u7F6E\u67E5\u8BE2\u56FE\u5C42\uFF0C\u518D\u6B21\u70B9\u51FB\u6309\u94AE\u53EF\u5173\u95ED",type:"success",duration:3e3})):((g=t.map)!=null&&g.mapContainer&&(t.map.mapContainer.style.cursor=""),(_=t.map)!=null&&_.map&&t.map.map.un("click",G),b({message:"\u67E5\u8BE2\u6A21\u5F0F\u5DF2\u5173\u95ED",type:"info",duration:2e3}))}async function G(n){const m=n.coordinate,g=ol(m,"EPSG:3857","EPSG:4326"),_={longitude:g[0],latitude:g[1]};W.value={..._},await w(g[1],g[0])}function O({layer:n,checked:m}){var g,_,D;try{const j=(g=o.mapConfig)==null?void 0:g.layers.find(p=>p.id===n.id);if(!j)return;if(m)if(j.visible=!0,(_=o.layerManager)==null?void 0:_.addLayer(j))K(n.id,!0),b.success(`\u5DF2\u52A0\u8F7D\u56FE\u5C42: ${ee(n.id)}`);else{b.error(`\u56FE\u5C42\u52A0\u8F7D\u5931\u8D25: ${ee(n.id)}`);const k=T.value.layers.find(P=>P.id===n.id);k&&(k.selected=!1)}else j.visible=!1,(D=o.layerManager)==null||D.removeLayer(n.id),K(n.id,!1),b.success(`\u5DF2\u79FB\u9664\u56FE\u5C42: ${ee(n.id)}`);o.refreshLayerGroups()}catch{b.error("\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}}async function w(n,m){var g;x.value="loading",C.value=!0;try{const _=rl(n,m,U.value),D=await rs.get(_);if(D.data.status==="success"){const j=D.data,p=(((g=o.mapConfig)==null?void 0:g.layers)||[]).map(P=>P.id),k=j.layers.filter(P=>p.includes(P.id));T.value={...j,count:k.length,layers:k},j.count,k.length,T.value.layers.forEach(P=>{var z;P.selected=(z=P.id,o.layerGroups.some(Q=>Q.layers.some(L=>L.id===z&&L.visible)))}),T.value.layers.length===0&&b({message:"\u8BE5\u4F4D\u7F6E\u672A\u627E\u5230\u56FE\u5C42",type:"info",duration:3e3}),x.value="success"}else $.value=D.data.message||"\u67E5\u8BE2\u5931\u8D25",x.value="error"}catch(_){$.value=_ instanceof Error?_.message:"\u7F51\u7EDC\u8BF7\u6C42\u5931\u8D25",x.value="error"}}async function h(){await w(W.value.latitude,W.value.longitude)}function ee(n){var g;const m=(((g=o.mapConfig)==null?void 0:g.layers)||[]).find(_=>_.id===n);return(m==null?void 0:m.name)||n}function K(n,m){o.layerGroups.forEach(g=>{const _=g.layers.find(D=>D.id===n);_&&(_.visible=m)})}Ce("layerQuery",{toggleQueryMode:q,isQueryModeActive:c}),we(async()=>{await async function(){var n,m;try{const g=await os("baseMap3");(m=(n=g.mapConfig)==null?void 0:n.queryConfig)!=null&&m.defaultWorkspace?U.value=g.mapConfig.queryConfig.defaultWorkspace:U.value="test_myworkspace"}catch{U.value="test_myworkspace"}}(),s.length>0&&V(s[0].name),v("initialized",0)}),Ee(()=>{c.value&&q()});const v=a;return(n,m)=>{const g=R("el-icon"),_=R("el-tooltip");return f(),F("div",Ot,[r("div",zt,[m[2]||(m[2]=r("div",{class:"hd-layer-manager-header"},[r("h2",{class:"hd-layer-manager-title"},"\u5FEB\u62FC\u56FE\u5C42")],-1)),r("div",Nt,[r("div",Ut,[i.value?(f(),ae(Ha(i.value),{key:0})):(f(),F("div",$t,m[1]||(m[1]=[r("p",null,"\u8BF7\u9009\u62E9\u53F3\u4FA7\u9762\u677F\u8FDB\u884C\u64CD\u4F5C",-1)])))]),r("div",Gt,[(f(!0),F(ve,null,Fe(s.filter(D=>!D.hidden),(D,j)=>(f(),F("div",{key:j,class:le(["panel-select-item",{active:l.value===D.name}]),onClick:p=>V(D.name)},[d(_,{content:D.label,placement:"left",enterable:!1},{default:u(()=>[r("div",Bt,[d(g,null,{default:u(()=>[(f(),ae(Ha(D.icon)))]),_:2},1024),r("div",jt,Y(D.label),1)])]),_:2},1032,["content"])],10,qt))),128))])])]),d(il,{visible:C.value,status:x.value,"query-layers":T.value.layers||[],error:$.value,"query-location":W.value,onClose:m[0]||(m[0]=D=>C.value=!1),onRetry:h,onToggleLayer:O},null,8,["visible","status","query-layers","error","query-location"])])}}},Wt={class:"layer-manager-container"},Kt={class:"panel-content"},Qt={class:"search-container"},Xt={class:"scrollable-content"},Zt={key:0,class:"empty-layer-message"},Jt={class:"custom-tree-node"},Yt={key:2,class:"layer-type-indicator"},ea={key:0,class:"style-preview"},ta={key:1,class:"style-preview"},aa={key:2,class:"style-preview"},sa={key:3,class:"raster-indicator"},la={key:3,class:"theme-indicator"},na={key:4,class:"theme-name"},ia={key:5,class:"layer-name"},oa={key:6,class:"layer-type"},ra={key:0,class:"node-actions"},da={class:"style-editor-content"},ca={key:0},ua={key:1},ha={class:"custom-select"},pa={key:2},ma={class:"style-preview-container"},ga={class:"preview-box"},va={key:0,class:"point-preview-large"},ya={key:1,class:"line-preview-large"},fa={key:2,class:"polygon-preview-large"},_a={class:"dialog-footer"},Ca={class:"label-editor-content"},ba={class:"dialog-footer"},wa={class:"upload-dialog-content"},ka={class:"upload-description"},xa={class:"upload-area"},Pa={key:0,class:"upload-progress"},La={class:"dialog-footer"},Ta=He(be({__name:"index",props:{visible:{type:Boolean,default:!0},position:{type:String,default:"left"}},emits:["initialized","toggle-collapse"],setup(E,{emit:e}){const a=S(""),s=S([]),l=S([]),i=S([]),o=S(null),t=Wa({visible:!1,currentLayer:null,geometryType:null,style:{color:"#000000",weight:1,opacity:1,fillColor:"#3388ff",lineDash:[],radius:6}}),c=Wa({visible:!1,uploading:!1,uploadProgress:0}),C=S(null),x=S([]),T=S("test_myworkspace"),$=he(()=>{if(!a.value)return s.value;const v=a.value.toLowerCase().trim(),n=g=>{const _={...g};return g.children&&(_.children=g.children.map(n)),_},m=g=>{const _=n(g);return _.name.toLowerCase().includes(v)||_.isTheme&&_.children&&_.children.length>0&&(_.children=_.children.map(m).filter(D=>D!==null),_.children.length>0)?_:null};return s.value.map(m).filter(g=>g!==null)}),U=()=>{var j;const v=me(),n=((j=v.mapConfig)==null?void 0:j.layers)||[],m=[...l.value],g={},_=[];n.forEach(p=>{const k=p,P={id:p.id,name:p.name,type:p.type,protocol:p.protocol,loaded:v.isLayerLoaded(p.id),visible:p.active!==!1,geometryType:p.geometryType,originalLayer:p};k.theme?(g[k.theme]||(g[k.theme]=[]),g[k.theme].push(P)):_.push(P)});const D=[];Object.keys(g).forEach(p=>{const k=g[p],P=k.filter(z=>z.loaded).length;D.push({id:`theme-${p}`,name:p,isTheme:!0,loaded:P===k.length,indeterminate:P>0&&P<k.length,children:k})}),D.push(..._),i.value=[...m],s.value=D},W=v=>{l.value.includes(v.id)||(l.value.push(v.id),i.value=[...l.value],localStorage.setItem("layerTree-expandedKeys",JSON.stringify(l.value)))},Z=v=>{const n=l.value.indexOf(v.id);n!==-1&&(l.value.splice(n,1),i.value=[...l.value],localStorage.setItem("layerTree-expandedKeys",JSON.stringify(l.value)))};Ka(()=>me().loadedLayers,()=>{const v=[...l.value];U(),Qa(()=>{l.value=v})},{deep:!0});const ie=v=>100*v+"%",ne=()=>{var _,D;if(!t.currentLayer)return void(t.visible=!1);const v=me(),n=t.currentLayer.id,m={color:t.style.color,weight:t.style.weight,opacity:t.style.opacity,fillColor:t.style.fillColor};(_=t.geometryType)!=null&&_.includes("Line")&&(m.lineDash=t.style.lineDash),(D=t.geometryType)!=null&&D.includes("Point")&&(m.radius=t.style.radius);const g=v.getLayerById(n);g&&(typeof g.defaultStyle!="string"&&Object.assign(g.defaultStyle||{},m),v.reloadLayer(n).then(j=>{j?b.success("\u6837\u5F0F\u5DF2\u66F4\u65B0"):b.error("\u6837\u5F0F\u66F4\u65B0\u5931\u8D25")})),t.visible=!1},B=()=>{U()},V=async()=>{var v,n;try{const m=await os("baseMap2");(n=(v=m.mapConfig)==null?void 0:v.queryConfig)!=null&&n.defaultWorkspace&&(T.value=m.mapConfig.queryConfig.defaultWorkspace)}catch{}},q=async()=>{try{const v=me(),n=b({message:"\u6B63\u5728\u66F4\u65B0\u5730\u56FE\u914D\u7F6E...",type:"info",duration:0,showClose:!1});try{await v.loadConfig()?(await V(),n.close(),b.success("\u5730\u56FE\u914D\u7F6E\u5DF2\u66F4\u65B0\uFF0C\u65B0\u4E0A\u4F20\u7684\u56FE\u5C42\u73B0\u5728\u53EF\u7528")):(n.close(),b.warning("\u5730\u56FE\u914D\u7F6E\u66F4\u65B0\u5931\u8D25\uFF0C\u4F46\u64CD\u4F5C\u5DF2\u5B8C\u6210"))}catch(m){throw n.close(),m}}catch{b.warning("\u5730\u56FE\u914D\u7F6E\u66F4\u65B0\u5931\u8D25\uFF0C\u4F46\u64CD\u4F5C\u5DF2\u5B8C\u6210")}},G=()=>{c.visible=!0,C.value=null,x.value=[],c.uploading=!1,c.uploadProgress=0},O=()=>{c.visible=!1,C.value=null,x.value=[],c.uploading=!1,c.uploadProgress=0},w=(v,n)=>(C.value=v.raw,x.value=n,!1),h=()=>{C.value=null,x.value=[]},ee=v=>["application/zip","application/x-zip-compressed","application/x-rar-compressed","application/x-7z-compressed","application/gzip","application/x-tar"].includes(v.type)||v.name.toLowerCase().endsWith(".zip")||v.name.toLowerCase().endsWith(".rar")||v.name.toLowerCase().endsWith(".7z")||v.name.toLowerCase().endsWith(".tar.gz")?v.size/1024/1024<50||(b.error("\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7 50MB\uFF01"),!1):(b.error("\u53EA\u652F\u6301\u4E0A\u4F20 ZIP\u3001RAR\u30017Z\u3001TAR.GZ \u7B49\u538B\u7F29\u683C\u5F0F\u7684\u6587\u4EF6\uFF01"),!1),K=async()=>{if(C.value){c.uploading=!0,c.uploadProgress=0;try{const v=new FormData;v.append("file",C.value),v.append("workspace",T.value);const n="http://**************:8091/api/map/archive/upload/";await new Promise((m,g)=>{const _=new XMLHttpRequest;_.upload.addEventListener("progress",D=>{D.lengthComputable&&(c.uploadProgress=Math.round(D.loaded/D.total*100))}),_.addEventListener("load",()=>{if(_.status>=200&&_.status<300)try{const D=JSON.parse(_.responseText);m(D)}catch{m({message:"\u4E0A\u4F20\u6210\u529F",response:_.responseText})}else g(new Error(`HTTP \u9519\u8BEF: ${_.status} ${_.statusText}`))}),_.addEventListener("error",()=>{g(new Error("\u7F51\u7EDC\u9519\u8BEF\u6216\u8BF7\u6C42\u5931\u8D25"))}),_.addEventListener("timeout",()=>{g(new Error("\u8BF7\u6C42\u8D85\u65F6"))}),_.open("POST",n),_.timeout=6e4,_.send(v)}),b.success("\u56FE\u5C42\u4E0A\u4F20\u6210\u529F\uFF01"),O(),await q(),B()}catch(v){b.error(`\u4E0A\u4F20\u5931\u8D25: ${v.message||"\u672A\u77E5\u9519\u8BEF"}`)}finally{c.uploading=!1,c.uploadProgress=0}}else b.warning("\u8BF7\u5148\u9009\u62E9\u8981\u4E0A\u4F20\u7684\u6587\u4EF6\uFF01")};return we(async()=>{await V(),s.value=[];try{const v=localStorage.getItem("layerTree-expandedKeys");if(v){const n=JSON.parse(v);l.value=n,i.value=n}}catch{}Ka(()=>me().mapConfig,v=>{v&&Qa(()=>{if(B(),l.value.length===0&&s.value.length>0){const n=s.value.filter(m=>m.isTheme).map(m=>m.id);l.value=n,i.value=n}})},{immediate:!0})}),(v,n)=>{const m=R("el-input"),g=R("el-button"),_=R("el-checkbox"),D=R("el-icon"),j=R("el-slider"),p=R("el-form-item"),k=R("el-input-number"),P=R("el-color-picker"),z=R("el-dialog"),Q=R("el-switch"),L=R("el-option"),J=R("el-select"),I=R("el-alert"),X=R("el-upload"),te=R("el-progress");return f(),F("div",Wt,[n[43]||(n[43]=r("div",{class:"layer-manager-header"},[r("h2",{class:"layer-manager-title"},"\u56FE\u5C42\u7BA1\u7406")],-1)),r("div",Kt,[r("div",Qt,[d(m,{modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=y=>a.value=y),placeholder:"\u641C\u7D22\u56FE\u5C42...","prefix-icon":"search",size:"small",clearable:"",class:"search-input"},null,8,["modelValue"]),d(g,{type:"primary",size:"small",onClick:G,class:"upload-button"},{default:u(()=>n[25]||(n[25]=[se(" \u4E0A\u4F20\u56FE\u5C42 ")])),_:1})]),r("div",Xt,[$.value.length?(f(),ae(M(hl),{data:$.value,props:{label:"name",children:"children"},"node-key":"id","default-expanded-keys":i.value,ref_key:"layerTreeRef",ref:o,"expand-on-click-node":!0,onNodeExpand:W,onNodeCollapse:Z,key:"layer-tree-static"},{default:u(({data:y})=>{return[r("div",Jt,[r("div",{class:le(["node-content",{"theme-node":y.isTheme}])},[y.isTheme?H("",!0):(f(),ae(_,{key:0,modelValue:y.loaded,"onUpdate:modelValue":de=>y.loaded=de,onChange:de=>(async N=>{const A=me();if(N.loaded)try{await A.addLayer(N.id)?b.success(`\u5DF2\u52A0\u8F7D\u56FE\u5C42: ${N.name}`):(N.loaded=!1,b.error(`\u52A0\u8F7D\u56FE\u5C42\u5931\u8D25: ${N.name}`))}catch(ce){N.loaded=!1,b.error(`\u52A0\u8F7D\u56FE\u5C42\u51FA\u9519: ${ce instanceof Error?ce.message:String(ce)}`)}else A.removeLayer(N.id)?b.success(`\u5DF2\u79FB\u9664\u56FE\u5C42: ${N.name}`):(N.loaded=!0,b.error(`\u79FB\u9664\u56FE\u5C42\u5931\u8D25: ${N.name}`));i.value=[...l.value],U()})(y),onClick:n[1]||(n[1]=ke(()=>{},["stop"]))},null,8,["modelValue","onUpdate:modelValue","onChange"])),y.isTheme?(f(),ae(_,{key:1,modelValue:y.loaded,"onUpdate:modelValue":de=>y.loaded=de,onChange:de=>(async N=>{const A=me(),ce=N.children||[];if(l.value.includes(N.id)||(l.value.push(N.id),i.value=[...l.value]),N.loaded){for(const ue of ce)if(!ue.loaded)try{await A.addLayer(ue.id)&&(ue.loaded=!0)}catch{}b.success(`\u5DF2\u52A0\u8F7D\u4E3B\u9898 ${N.name} \u7684\u6240\u6709\u56FE\u5C42`)}else{let ue=0;for(const ge of ce)ge.loaded&&A.removeLayer(ge.id)&&(ge.loaded=!1,ue++);b.success(`\u5DF2\u79FB\u9664\u4E3B\u9898 ${N.name} \u7684 ${ue} \u4E2A\u56FE\u5C42`)}i.value=[...l.value],U()})(y),onClick:n[2]||(n[2]=ke(()=>{},["stop"])),indeterminate:y.indeterminate},null,8,["modelValue","onUpdate:modelValue","onChange","indeterminate"])):H("",!0),y.isTheme?H("",!0):(f(),F("div",Yt,[y.geometryType==="Point"?(f(),F("div",ea,n[26]||(n[26]=[r("div",{class:"point-preview"},[r("div",{class:"point-inner"})],-1)]))):y.geometryType==="LineString"?(f(),F("div",ta,n[27]||(n[27]=[r("div",{class:"line-preview"},[r("div",{class:"line-inner"})],-1)]))):y.geometryType==="Polygon"?(f(),F("div",aa,n[28]||(n[28]=[r("div",{class:"polygon-preview"},[r("div",{class:"polygon-inner"})],-1)]))):(f(),F("div",sa,n[29]||(n[29]=[r("div",{class:"raster-icon"},[r("div",{class:"raster-grid"})],-1)])))])),y.isTheme?(f(),F("div",la)):H("",!0),y.isTheme?(f(),F("span",na,Y(y.name),1)):(f(),F("span",ia,Y(y.name),1)),y.isTheme?H("",!0):(f(),F("span",oa,Y(M(dl)(y)),1))],2),y.isTheme?H("",!0):(f(),F("div",ra,[y.loaded?(f(),ae(g,{key:0,size:"small",type:"text",onClick:ke(de=>(N=>{if(N&&N.loaded)try{gl.currentLayer=N,vl()}catch(A){b.error(`\u5904\u7406\u56FE\u5C42\u7F29\u653E\u65F6\u51FA\u9519: ${A}`)}})(y),["stop"]),title:"\u7F29\u653E\u81F3\u56FE\u5C42\u8303\u56F4"},{default:u(()=>[d(D,null,{default:u(()=>[d(M(cl))]),_:1})]),_:2},1032,["onClick"])):H("",!0),y.loaded&&(oe=y,oe&&oe.originalLayer&&oe.originalLayer.theme==="\u4E34\u65F6\u6587\u4EF6")?(f(),ae(g,{key:1,size:"small",type:"text",onClick:ke(de=>(async N=>{if(!N||!N.originalLayer)return void b.error("\u56FE\u5C42\u4FE1\u606F\u4E0D\u5B8C\u6574");const A=N.originalLayer;try{await yl.confirm(`\u786E\u5B9A\u8981\u5220\u9664\u4E34\u65F6\u6587\u4EF6\u56FE\u5C42 "${A.name}" \u5417\uFF1F\u6B64\u64CD\u4F5C\u5C06\u5220\u9664\u672C\u5730\u6587\u4EF6\u3001GeoServer\u56FE\u5C42\u548C\u5730\u56FE\u914D\u7F6E\uFF0C\u4E14\u4E0D\u53EF\u6062\u590D\u3002`,"\u5220\u9664\u786E\u8BA4",{confirmButtonText:"\u786E\u5B9A\u5220\u9664",cancelButtonText:"\u53D6\u6D88",type:"warning",confirmButtonClass:"el-button--danger"})}catch{return}try{const ce=`http://**************:8091/api/map/archive/delete/?workspace=${encodeURIComponent(A.workspace||T.value)}&layer_name=${encodeURIComponent(A.layerName||A.name)}`,ue=await fetch(ce,{method:"GET"});if(!ue.ok)throw new Error(`HTTP \u9519\u8BEF: ${ue.status} ${ue.statusText}`);let ge;try{ge=await ue.json()}catch{ge={message:"\u5220\u9664\u6210\u529F"}}me().removeLayer(N.id),b.success(`\u4E34\u65F6\u6587\u4EF6\u56FE\u5C42 "${A.name}" \u5220\u9664\u6210\u529F\uFF01`),await q(),B()}catch(ce){b.error(`\u5220\u9664\u5931\u8D25: ${ce.message||"\u672A\u77E5\u9519\u8BEF"}`)}})(y),["stop"]),title:"\u5220\u9664\u4E34\u65F6\u6587\u4EF6",class:"delete-button"},{default:u(()=>[d(D,null,{default:u(()=>[d(M(ul))]),_:1})]),_:2},1032,["onClick"])):H("",!0)]))])];var oe}),_:1},8,["data","default-expanded-keys"])):(f(),F("div",Zt," \u6682\u65E0\u56FE\u5C42\u53EF\u7528 "))])]),d(z,{modelValue:t.visible,"onUpdate:modelValue":n[17]||(n[17]=y=>t.visible=y),title:"\u4FEE\u6539\u56FE\u5C42\u6837\u5F0F",width:"400px","close-on-click-modal":!1,"append-to-body":""},{footer:u(()=>[r("span",_a,[d(g,{onClick:n[16]||(n[16]=y=>t.visible=!1)},{default:u(()=>n[34]||(n[34]=[se("\u53D6\u6D88")])),_:1}),d(g,{type:"primary",onClick:ne},{default:u(()=>n[35]||(n[35]=[se("\u5E94\u7528")])),_:1})])]),default:u(()=>{var y,oe,de,N;return[r("div",da,[t.currentLayer?(f(),F(ve,{key:0},[r("h3",null,Y(t.currentLayer.name),1),d(p,{label:"\u900F\u660E\u5EA6"},{default:u(()=>[d(j,{modelValue:t.style.opacity,"onUpdate:modelValue":n[3]||(n[3]=A=>t.style.opacity=A),min:0,max:1,step:.1,"format-tooltip":ie},null,8,["modelValue"])]),_:1}),(y=t.geometryType)!=null&&y.includes("Point")?(f(),F("div",ca,[d(p,{label:"\u70B9\u5927\u5C0F"},{default:u(()=>[d(k,{modelValue:t.style.radius,"onUpdate:modelValue":n[4]||(n[4]=A=>t.style.radius=A),min:2,max:20,size:"small"},null,8,["modelValue"])]),_:1}),d(p,{label:"\u8FB9\u6846\u7C97\u7EC6"},{default:u(()=>[d(k,{modelValue:t.style.weight,"onUpdate:modelValue":n[5]||(n[5]=A=>t.style.weight=A),min:0,max:5,size:"small"},null,8,["modelValue"])]),_:1}),d(p,{label:"\u8FB9\u6846\u989C\u8272"},{default:u(()=>[d(P,{modelValue:t.style.color,"onUpdate:modelValue":n[6]||(n[6]=A=>t.style.color=A)},null,8,["modelValue"])]),_:1}),d(p,{label:"\u586B\u5145\u989C\u8272"},{default:u(()=>[d(P,{modelValue:t.style.fillColor,"onUpdate:modelValue":n[7]||(n[7]=A=>t.style.fillColor=A),"show-alpha":""},null,8,["modelValue"])]),_:1})])):(oe=t.geometryType)!=null&&oe.includes("Line")?(f(),F("div",ua,[d(p,{label:"\u7EBF\u6761\u7C97\u7EC6"},{default:u(()=>[d(k,{modelValue:t.style.weight,"onUpdate:modelValue":n[8]||(n[8]=A=>t.style.weight=A),min:1,max:10,size:"small"},null,8,["modelValue"])]),_:1}),d(p,{label:"\u7EBF\u6761\u989C\u8272"},{default:u(()=>[d(P,{modelValue:t.style.color,"onUpdate:modelValue":n[9]||(n[9]=A=>t.style.color=A)},null,8,["modelValue"])]),_:1}),d(p,{label:"\u7EBF\u6761\u6837\u5F0F"},{default:u(()=>[r("div",ha,[r("div",{class:le(["select-item",{active:Array.isArray(t.style.lineDash)&&t.style.lineDash.length===0}]),onClick:n[10]||(n[10]=A=>t.style.lineDash=[])},n[30]||(n[30]=[r("div",{class:"line-preview solid"},null,-1),r("span",null,"\u5B9E\u7EBF",-1)]),2),r("div",{class:le(["select-item",{active:Array.isArray(t.style.lineDash)&&t.style.lineDash.toString()==="4,8"}]),onClick:n[11]||(n[11]=A=>t.style.lineDash=[4,8])},n[31]||(n[31]=[r("div",{class:"line-preview dashed"},null,-1),r("span",null,"\u865A\u7EBF",-1)]),2),r("div",{class:le(["select-item",{active:Array.isArray(t.style.lineDash)&&t.style.lineDash.toString()==="1,4,8,4"}]),onClick:n[12]||(n[12]=A=>t.style.lineDash=[1,4,8,4])},n[32]||(n[32]=[r("div",{class:"line-preview dotted"},null,-1),r("span",null,"\u70B9\u5212\u7EBF",-1)]),2)])]),_:1})])):(f(),F("div",pa,[d(p,{label:"\u8FB9\u6846\u7C97\u7EC6"},{default:u(()=>[d(k,{modelValue:t.style.weight,"onUpdate:modelValue":n[13]||(n[13]=A=>t.style.weight=A),min:0,max:10,size:"small"},null,8,["modelValue"])]),_:1}),d(p,{label:"\u8FB9\u6846\u989C\u8272"},{default:u(()=>[d(P,{modelValue:t.style.color,"onUpdate:modelValue":n[14]||(n[14]=A=>t.style.color=A)},null,8,["modelValue"])]),_:1}),d(p,{label:"\u586B\u5145\u989C\u8272"},{default:u(()=>[d(P,{modelValue:t.style.fillColor,"onUpdate:modelValue":n[15]||(n[15]=A=>t.style.fillColor=A),"show-alpha":""},null,8,["modelValue"])]),_:1})])),r("div",ma,[n[33]||(n[33]=r("h4",null,"\u6837\u5F0F\u9884\u89C8",-1)),r("div",ga,[(de=t.geometryType)!=null&&de.includes("Point")?(f(),F("div",va,[r("div",{class:"point-inner",style:De({width:2*t.style.radius+"px",height:2*t.style.radius+"px",backgroundColor:t.style.fillColor,borderColor:t.style.color,borderWidth:t.style.weight+"px",opacity:t.style.opacity})},null,4)])):(N=t.geometryType)!=null&&N.includes("Line")?(f(),F("div",ya,[r("div",{class:"line-inner",style:De({borderTop:`${t.style.weight}px ${Array.isArray(t.style.lineDash)&&t.style.lineDash.length>0?t.style.lineDash.toString()==="4,8"?"dashed":"dotted":"solid"} ${t.style.color}`,opacity:t.style.opacity})},null,4)])):(f(),F("div",fa,[r("div",{class:"polygon-inner",style:De({backgroundColor:t.style.fillColor,borderColor:t.style.color,borderWidth:t.style.weight+"px",opacity:t.style.opacity})},null,4)]))])])],64)):H("",!0)])]}),_:1},8,["modelValue"]),d(z,{modelValue:M(re).visible,"onUpdate:modelValue":n[23]||(n[23]=y=>M(re).visible=y),title:"\u8BBE\u7F6E\u56FE\u5C42\u6807\u6CE8",width:"400px","close-on-click-modal":!1,"append-to-body":""},{footer:u(()=>[r("span",ba,[d(g,{onClick:n[22]||(n[22]=y=>M(re).visible=!1)},{default:u(()=>n[36]||(n[36]=[se("\u53D6\u6D88")])),_:1}),d(g,{type:"primary",onClick:M(pl)},{default:u(()=>n[37]||(n[37]=[se("\u5E94\u7528")])),_:1},8,["onClick"])])]),default:u(()=>[r("div",Ca,[M(re).currentLayer?(f(),F(ve,{key:0},[r("h3",null,Y(M(re).currentLayer.name),1),d(p,{label:"\u663E\u793A\u6807\u6CE8"},{default:u(()=>[d(Q,{modelValue:M(re).showLabels,"onUpdate:modelValue":n[18]||(n[18]=y=>M(re).showLabels=y)},null,8,["modelValue"])]),_:1}),M(re).showLabels?(f(),ae(p,{key:0,label:"\u6807\u6CE8\u5B57\u6BB5"},{default:u(()=>[d(J,{modelValue:M(re).selectedField,"onUpdate:modelValue":n[19]||(n[19]=y=>M(re).selectedField=y),placeholder:"\u9009\u62E9\u6807\u6CE8\u5B57\u6BB5"},{default:u(()=>[(f(!0),F(ve,null,Fe(M(re).availableFields,y=>(f(),ae(L,{key:y,label:y,value:y},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):H("",!0),M(re).showLabels?(f(),F(ve,{key:1},[d(p,{label:"\u5B57\u4F53\u5927\u5C0F"},{default:u(()=>[d(j,{modelValue:M(re).fontSize,"onUpdate:modelValue":n[20]||(n[20]=y=>M(re).fontSize=y),min:8,max:24,step:1},null,8,["modelValue"])]),_:1}),d(p,{label:"\u6587\u5B57\u989C\u8272"},{default:u(()=>[d(P,{modelValue:M(re).labelColor,"onUpdate:modelValue":n[21]||(n[21]=y=>M(re).labelColor=y)},null,8,["modelValue"])]),_:1})],64)):H("",!0)],64)):H("",!0)])]),_:1},8,["modelValue"]),d(z,{modelValue:c.visible,"onUpdate:modelValue":n[24]||(n[24]=y=>c.visible=y),title:"\u4E0A\u4F20\u56FE\u5C42",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:O},{footer:u(()=>[r("div",La,[d(g,{onClick:O,disabled:c.uploading},{default:u(()=>n[42]||(n[42]=[se(" \u53D6\u6D88 ")])),_:1},8,["disabled"]),d(g,{type:"primary",onClick:K,loading:c.uploading,disabled:!C.value},{default:u(()=>[se(Y(c.uploading?"\u4E0A\u4F20\u4E2D...":"\u5F00\u59CB\u4E0A\u4F20"),1)]),_:1},8,["loading","disabled"])])]),default:u(()=>[r("div",wa,[r("div",ka,[d(I,{title:"\u4E0A\u4F20\u56FE\u5C42\u6587\u4EF6",type:"info",closable:!1,"show-icon":""},{default:u(()=>n[38]||(n[38]=[r("p",null,"\u652F\u6301\u4E0A\u4F20\u4EE5\u4E0B\u538B\u7F29\u683C\u5F0F\u7684 Shapefile \u6587\u4EF6\uFF1A",-1),r("ul",null,[r("li",null,"ZIP \u683C\u5F0F (.zip) - \u63A8\u8350"),r("li",null,"RAR \u683C\u5F0F (.rar)"),r("li",null,"7Z \u683C\u5F0F (.7z)"),r("li",null,"TAR.GZ \u683C\u5F0F (.tar.gz)")],-1),r("p",{class:"file-size-tip"},"\u6587\u4EF6\u5927\u5C0F\u9650\u5236\uFF1A500MB",-1)])),_:1})]),r("div",xa,[d(X,{ref:"uploadRef","file-list":x.value,"before-upload":ee,"on-change":w,"on-remove":h,"auto-upload":!1,limit:1,drag:"",accept:".zip,.rar,.7z,.tar.gz"},{tip:u(()=>n[39]||(n[39]=[r("div",{class:"el-upload__tip"}," \u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u538B\u7F29\u6587\u4EF6\uFF0C\u4E14\u4E0D\u8D85\u8FC7500MB ",-1)])),default:u(()=>[d(D,{class:"el-icon--upload"},{default:u(()=>[d(M(ml))]),_:1}),n[40]||(n[40]=r("div",{class:"el-upload__text"},[se(" \u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),r("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list"])]),c.uploading?(f(),F("div",Pa,[d(te,{percentage:c.uploadProgress,"stroke-width":8,status:"success"},null,8,["percentage"]),n[41]||(n[41]=r("p",{class:"progress-text"},"\u6B63\u5728\u4E0A\u4F20\u56FE\u5C42\u6587\u4EF6...",-1))])):H("",!0)])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-d0cfc4d0"]]),Va={class:"icon-container"},Sa={class:"gis-tools-content"},Ia={class:"gis-tool-item"},Ma={class:"gis-tool-item"},Ea={class:"gis-tool-item"},Da={class:"gis-tool-item"},Fa={class:"gis-tool-item"},Aa={class:"gis-tool-item"},Ra={class:"gis-tool-item"},Oa={class:"gis-tool-item"},za={class:"gis-tool-item"},Na=be({__name:"index",props:{mapStore:{type:Object,required:!0}},emits:["drawStart","drawEnd","attributeInfo"],setup(E,{expose:e,emit:a}){const s=E,l=a,i=S(!1),o=S(!1),t=S(""),c=S(new Za),C=S(new Xa({source:c.value,style:new ds({fill:new Re({color:"rgba(64, 158, 255, 0.4)"}),stroke:new us({color:"#409eff",width:2}),image:new cs({radius:7,fill:new Re({color:"#409eff"})})})})),x=S(null),T=S(null),$=S(null),U=S(null),W=S(null);function Z(){i.value=!i.value,i.value?setTimeout(()=>{o.value=!0},300):o.value=!1}function ie(){t.value==="Point"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u7ED8\u5236\u70B9")):(O(),t.value="Point",G("Point"),b.success("\u5DF2\u6FC0\u6D3B\u7ED8\u5236\u70B9\u5DE5\u5177"))}function ne(){t.value==="LineString"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u7ED8\u5236\u7EBF")):(O(),t.value="LineString",G("LineString"),b.success("\u5DF2\u6FC0\u6D3B\u7ED8\u5236\u7EBF\u5DE5\u5177"))}function B(){t.value==="Polygon"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u7ED8\u5236\u9762")):(O(),t.value="Polygon",G("Polygon"),b.success("\u5DF2\u6FC0\u6D3B\u7ED8\u5236\u9762\u5DE5\u5177"))}function V(){t.value==="Rectangle"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u7ED8\u5236\u77E9\u5F62")):(O(),t.value="Rectangle",G("LineString","Rectangle"),b.success("\u5DF2\u6FC0\u6D3B\u7ED8\u5236\u77E9\u5F62\u5DE5\u5177"))}function q(){t.value==="Circle"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u7ED8\u5236\u5706")):(O(),t.value="Circle",G("Circle"),b.success("\u5DF2\u6FC0\u6D3B\u7ED8\u5236\u5706\u5DE5\u5177"))}function G(p,k=null){var Q;const P=(Q=s.mapStore.map)==null?void 0:Q.map;if(!P)return;P.getLayers().getArray().includes(C.value)||P.addLayer(C.value);const z={source:c.value,type:p,condition:function(L){return!0},style:new ds({fill:new Re({color:"rgba(64, 158, 255, 0.4)"}),stroke:new us({color:"#409eff",width:2}),image:new cs({radius:7,fill:new Re({color:"#409eff"})})})};k==="Rectangle"&&(z.type="Circle",z.geometryFunction=function(L,J,I){const X=Ya([L[0],L[L.length-1]].map(function(y){return qe(y)})),te=[[ls(X),Ws(X),Ks(X),Qs(X),ls(X)]];return J?J.setCoordinates(te):J=new Ae(te),J}),x.value=new ys(z),P.addInteraction(x.value),x.value.on("drawstart",L=>{U.value=L.feature,P.getTargetElement().addEventListener("contextmenu",w),l("drawStart",{tool:t.value}),t.value!=="MeasureDistance"&&t.value!=="MeasureArea"||(n(),W.value=U.value.getGeometry().on("change",J=>{const I=J.target;let X;I instanceof Ae?(X=function(te){const y=Vl(te);let oe;return oe=y>1e4?Math.round(y/1e6*100)/100+" km\xB2":Math.round(100*y)/100+" m\xB2",oe}(I),T.value.innerHTML=X,$.value.setPosition(I.getInteriorPoint().getCoordinates())):I instanceof Pe&&(X=function(te){const y=Tl(te);let oe;return oe=y>1e3?Math.round(y/1e3*100)/100+" km":Math.round(100*y)/100+" m",oe}(I),T.value.innerHTML=X,$.value.setPosition(I.getLastCoordinate()))}))}),x.value.on("drawend",L=>{P.getTargetElement().removeEventListener("contextmenu",w),l("drawEnd",{tool:t.value,feature:L.feature}),t.value!=="MeasureDistance"&&t.value!=="MeasureArea"||(T.value&&(T.value.className="ol-tooltip ol-tooltip-static"),$.value&&$.value.setOffset([0,-7]),U.value=null,T.value=null,n(),W.value&&hs(W.value))})}function O(){var k;const p=(k=s.mapStore.map)==null?void 0:k.map;p&&x.value&&(p.removeInteraction(x.value),x.value=null)}function w(p){if(p.preventDefault(),U.value&&x.value)x.value.removeLastPoint();else if(t.value){const k=m(t.value);t.value="",O(),b.info(`\u5DF2\u53D6\u6D88${k}`)}}function h(p){if(p.preventDefault(),U.value&&x.value)x.value.removeLastPoint();else if(t.value){const k=m(t.value);t.value="",O(),b.info(`\u5DF2\u53D6\u6D88${k}`)}}function ee(){c.value&&(c.value.clear(),b.success("\u5DF2\u6E05\u9664\u6240\u6709\u7ED8\u5236"),document.querySelectorAll(".ol-tooltip").forEach(p=>{p.parentNode&&p.parentNode.removeChild(p)}))}function K(){t.value==="MeasureDistance"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u6D4B\u91CF\u8DDD\u79BB")):(O(),t.value="MeasureDistance",G("LineString"),b.success("\u5DF2\u6FC0\u6D3B\u6D4B\u91CF\u8DDD\u79BB\u5DE5\u5177"))}function v(){t.value==="MeasureArea"?(t.value="",O(),b.info("\u5DF2\u53D6\u6D88\u6D4B\u91CF\u9762\u79EF")):(O(),t.value="MeasureArea",G("Polygon"),b.success("\u5DF2\u6FC0\u6D3B\u6D4B\u91CF\u9762\u79EF\u5DE5\u5177"))}function n(){var k;const p=(k=s.mapStore.map)==null?void 0:k.map;p&&(T.value&&T.value.parentNode&&T.value.parentNode.removeChild(T.value),T.value=document.createElement("div"),T.value.className="ol-tooltip ol-tooltip-measure",$.value=new ms({element:T.value,offset:[0,-15],positioning:"bottom-center",stopEvent:!1,insertFirst:!1}),p.addOverlay($.value))}function m(p){return{Point:"\u7ED8\u5236\u70B9",LineString:"\u7ED8\u5236\u7EBF",Polygon:"\u7ED8\u5236\u9762",Rectangle:"\u7ED8\u5236\u77E9\u5F62",Circle:"\u7ED8\u5236\u5706",MeasureDistance:"\u6D4B\u91CF\u8DDD\u79BB",MeasureArea:"\u6D4B\u91CF\u9762\u79EF",AttributeInfo:"\u5C5E\u6027\u67E5\u770B",Edit:"\u7F16\u8F91","":"\u7ED8\u5236"}[p]||"\u7ED8\u5236"}function g(){t.value==="AttributeInfo"?(t.value="",D(),b.info("\u5DF2\u53D6\u6D88\u5C5E\u6027\u67E5\u770B")):(O(),t.value="AttributeInfo",function(){var k;const p=(k=s.mapStore.map)==null?void 0:k.map;p&&(_=p.on("singleclick",j))}(),b.success("\u5DF2\u6FC0\u6D3B\u5C5E\u6027\u67E5\u770B\u5DE5\u5177"))}let _=null;function D(){_&&(hs(_),_=null)}function j(p){var Q;const k=(Q=s.mapStore.map)==null?void 0:Q.map;if(!k||t.value!=="AttributeInfo")return;const P=k.getLayers().getArray().filter(L=>{var I;const J=(I=L.getSource)==null?void 0:I.call(L);return J&&J.getFeatureInfoUrl});if(P.length===0)return;const z=P.map(L=>{var de;const J=L.getSource(),I=k.getView(),X=I.getResolution();let te=((de=J.params_)==null?void 0:de.LAYERS)||"";const y=L.get("title")||L.get("name")||""||function(N){if(!N)return"\u672A\u77E5\u56FE\u5C42";const A=N.split(":");return(A.length>1?A[1]:A[0]).replace(/[_-]/g," ").replace(/\w\S*/g,ce=>ce.charAt(0).toUpperCase()+ce.substr(1).toLowerCase())}(te),oe=J.getFeatureInfoUrl(p.coordinate,X,I.getProjection().getCode(),{INFO_FORMAT:"application/json"});return oe?rs.get(oe).then(N=>({layerId:te,layerName:y,data:N.data,coordinate:p.coordinate})).catch(()=>null):Promise.resolve(null)});Promise.all(z).then(L=>{const J=L.filter(I=>I&&I.data&&I.data.features&&I.data.features.length>0);if(J.length>0){const I=J[0],X=I.data.features[0];l("attributeInfo",{layerId:I.layerId,layerName:I.layerName,attributes:X.properties,coordinate:I.coordinate})}else b.info("\u70B9\u51FB\u4F4D\u7F6E\u6CA1\u6709\u627E\u5230\u8981\u7D20")}).catch(L=>{b.error("\u83B7\u53D6\u8981\u7D20\u4FE1\u606F\u5931\u8D25")})}return we(()=>{var k;i.value=!1,o.value=!1;const p=(k=s.mapStore.map)==null?void 0:k.map;if(p){p.addLayer(C.value);const P=p.getTargetElement();P&&P.addEventListener("contextmenu",h)}}),Ee(()=>{var k;const p=(k=s.mapStore.map)==null?void 0:k.map;if(p){const P=p.getTargetElement();P&&P.removeEventListener("contextmenu",h),p.getLayers().getArray().includes(C.value)&&p.removeLayer(C.value),x.value&&p.removeInteraction(x.value),$.value&&p.removeOverlay($.value),D()}}),e({clearDrawings:ee,getActiveDrawTool:()=>t.value,removeDrawInteraction:O,removeAttributeInfoClick:D,activateAttributeInfo:g}),(p,k)=>{const P=R("el-icon"),z=R("el-button"),Q=R("el-tooltip");return f(),F("div",{class:le(["gis-tools-panel",{collapsed:i.value}])},[r("div",{class:"gis-tools-collapse-btn panel-button-horizontal",onClick:Z},[r("div",Va,[d(P,null,{default:u(()=>[i.value?(f(),ae(M(Ke),{key:1})):(f(),ae(M(We),{key:0}))]),_:1})])]),r("div",Sa,[r("div",Ia,[d(Q,{content:"\u5C5E\u6027\u67E5\u770B",placement:"bottom"},{default:u(()=>[d(z,{type:"warning",size:"small",onClick:g,class:le({active:t.value==="AttributeInfo"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(fl))]),_:1})]),_:1},8,["class"])]),_:1})]),k[0]||(k[0]=r("div",{class:"gis-tool-divider"},null,-1)),r("div",Ma,[d(Q,{content:"\u7ED8\u5236\u70B9",placement:"bottom"},{default:u(()=>[d(z,{type:"primary",size:"small",onClick:ie,class:le({active:t.value==="Point"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(_l))]),_:1})]),_:1},8,["class"])]),_:1})]),r("div",Ea,[d(Q,{content:"\u7ED8\u5236\u7EBF",placement:"bottom"},{default:u(()=>[d(z,{type:"primary",size:"small",onClick:ne,class:le({active:t.value==="LineString"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(Cl))]),_:1})]),_:1},8,["class"])]),_:1})]),r("div",Da,[d(Q,{content:"\u7ED8\u5236\u9762",placement:"bottom"},{default:u(()=>[d(z,{type:"primary",size:"small",onClick:B,class:le({active:t.value==="Polygon"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(bl))]),_:1})]),_:1},8,["class"])]),_:1})]),r("div",Fa,[d(Q,{content:"\u7ED8\u5236\u77E9\u5F62",placement:"bottom"},{default:u(()=>[d(z,{type:"primary",size:"small",onClick:V,class:le({active:t.value==="Rectangle"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(wl))]),_:1})]),_:1},8,["class"])]),_:1})]),r("div",Aa,[d(Q,{content:"\u7ED8\u5236\u5706",placement:"bottom"},{default:u(()=>[d(z,{type:"primary",size:"small",onClick:q,class:le({active:t.value==="Circle"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(kl))]),_:1})]),_:1},8,["class"])]),_:1})]),r("div",Ra,[d(Q,{content:"\u6D4B\u91CF\u8DDD\u79BB",placement:"bottom"},{default:u(()=>[d(z,{type:"success",size:"small",onClick:K,class:le({active:t.value==="MeasureDistance"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(xl))]),_:1})]),_:1},8,["class"])]),_:1})]),r("div",Oa,[d(Q,{content:"\u6D4B\u91CF\u9762\u79EF",placement:"bottom"},{default:u(()=>[d(z,{type:"success",size:"small",onClick:v,class:le({active:t.value==="MeasureArea"})},{default:u(()=>[d(P,null,{default:u(()=>[d(M(Pl))]),_:1})]),_:1},8,["class"])]),_:1})]),k[1]||(k[1]=r("div",{class:"gis-tool-divider"},null,-1)),r("div",za,[d(Q,{content:"\u6E05\u9664\u6240\u6709\u7ED8\u5236",placement:"bottom"},{default:u(()=>[d(z,{type:"danger",size:"small",onClick:ee},{default:u(()=>[d(P,null,{default:u(()=>[d(M(Ll))]),_:1})]),_:1})]),_:1})])])],2)}}}),Ua={class:"one-map-container"},$a={class:"title-bar"},Ga={class:"right-buttons"},qa={class:"icon-container"},Ba={class:"icon-container"},ps=be({__name:"index",setup(E){const e=Cs(),a=je(),s=S(null),l=S(null),i=S(null);S(null);const o=S(null),t=S(!1),c=S(!1);function C(){t.value=!t.value}function x(){c.value=!c.value}function T(){const V=e.resolve({path:"/bigScreen"});window.open(V.href,"_blank")}const $=async()=>{e.push("/home")};function U(V){}function W(V){}function Z(V){}function ie(V){}function ne(V){i.value?(c.value&&(c.value=!1),i.value.showAttributesPanel(V.attributes,V.layerId,`${V.layerName}-\u5C5E\u6027\u4FE1\u606F`)):b.warning("\u9AD8\u7CBE\u5EA6\u56FE\u5C42\u9762\u677F\u672A\u521D\u59CB\u5316\uFF0C\u65E0\u6CD5\u663E\u793A\u5C5E\u6027\u4FE1\u606F")}function B(){var V;(V=a.map)!=null&&V.map&&a.map.map.updateSize()}return we(async()=>{var V;s.value&&(a.initMap(s.value),(V=a.map)!=null&&V.map&&a.map.map.once("rendercomplete",()=>{})),t.value=!1,c.value=!1,window.addEventListener("resize",B)}),Ee(()=>{var V;(V=a.map)!=null&&V.map&&a.map.map.setTarget(void 0),a.clearCameraMonitor(),window.removeEventListener("resize",B)}),(V,q)=>{const G=R("el-button"),O=R("el-tooltip"),w=R("el-icon");return f(),F("div",Ua,[r("div",$a,[q[2]||(q[2]=r("div",{class:"left-title"},[r("img",{src:_s,alt:"Logo",class:"logo-image"}),r("span",{class:"title-text"},'\u6276\u7EE5\u53BF\u81EA\u7136\u8D44\u6E90"\u4E00\u5F20\u56FE"\u7CFB\u7EDF')],-1)),r("div",Ga,[d(O,{content:"\u6570\u636E\u5927\u5C4F",placement:"bottom",effect:"dark"},{default:u(()=>[d(G,{onClick:T,class:"nav-button"},{default:u(()=>q[0]||(q[0]=[se("\u6570\u636E\u5927\u5C4F")])),_:1})]),_:1}),d(O,{content:"\u9996\u9875",placement:"bottom",effect:"dark"},{default:u(()=>[d(G,{onClick:$,class:"nav-button"},{default:u(()=>q[1]||(q[1]=[se("\u9996\u9875")])),_:1})]),_:1})])]),r("div",{id:"map",ref_key:"mapContainer",ref:s},null,512),r("div",{class:le(["layer-panel",{collapsed:t.value}])},[d(M(Ta),{visible:!0,position:"left",onInitialized:U,onToggleCollapse:C,ref_key:"layerManagerRef",ref:l},null,512)],2),r("div",{class:le(["layer-panel-collapse-btn panel-button-base",{collapsed:t.value}]),onClick:C},[r("div",qa,[d(w,null,{default:u(()=>[t.value?(f(),ae(M(We),{key:1})):(f(),ae(M(Ke),{key:0}))]),_:1})])],2),r("div",{class:le(["hd-layer-panel",{collapsed:c.value}])},[d(M(Ht),{visible:!0,position:"right",onInitialized:W,ref_key:"hdLayerManagerRef",ref:i},null,512)],2),r("div",{class:le(["hd-layer-panel-collapse-btn panel-button-base",{collapsed:c.value}]),onClick:x},[r("div",Ba,[d(w,null,{default:u(()=>[c.value?(f(),ae(M(Ke),{key:1})):(f(),ae(M(We),{key:0}))]),_:1})])],2),d(M(Na),{mapStore:M(a),onDrawStart:Z,onDrawEnd:ie,onAttributeInfo:ne,ref_key:"gisToolsRef",ref:o},null,8,["mapStore"])])}}})});export{Il as __tla,ps as default};
