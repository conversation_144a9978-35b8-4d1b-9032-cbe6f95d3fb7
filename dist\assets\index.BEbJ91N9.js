import{b as sa,__tla as ua}from"./auditDepartment.B03bc9Ue.js";import{_ as ia,S as oa,g as da,a as ca,b as va,__tla as pa}from"./index.sXUh2gtl.js";import{g as ga,__tla as ha}from"./businessType.CgPPyj49.js";import{V as ma,p as _a,__tla as ya}from"./form.DeeIwgt1.js";import{d as fa,k as s,o as ba,B as d,m as wa,a as y,b as f,t as l,v as c,f as e,F as xa,p as Ca,q as za,G as v,E as Ta}from"./vue.CnN__PXn.js";import{q as ka,__tla as Ea}from"./index.BSP3cg_z.js";import"./echarts.DrVj8Jfx.js";import"./mapUrlReplacer.Bfjb2Uah.js";let W,Pa=Promise.all([(()=>{try{return ua}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return Ea}catch{}})()]).then(async()=>{let b,w,x,C,z,T,k,E,P,D,I,N,S,q,L,O;b={class:"layout-padding"},w={class:"detail-card"},x={class:"detail-container"},C={style:{padding:"5px"}},z={class:"detail-container"},T={class:"echarts-div"},k={class:"detail-container"},E={class:"echarts-div"},P={class:"detail-container event-detail"},D={style:{"margin-bottom":"10px"}},I={rowspan:"4"},N={colspan:"1"},S={colspan:"4"},q=["src"],L={style:{"margin-right":"20px"}},O=fa({__name:"index",setup(Da){const U=s([]),o=s({current:1,size:10}),$=s(1),m=s(!1),_=()=>{m.value=!0,_a(o.value).then(a=>{U.value=a.data.records,$.value=a.data.total,m.value=!1})},A=a=>{o.size=a,_()},H=a=>{o.current=a,_()},p=s(null),J=()=>{p.value&&p.value.closeDialog()},j=s([]),B=s([]);s({cityCode:"",businessType:"",businessEvent:""});const K=s([]);s([]);const M=s([]),Q=async()=>{try{_(),sa().then(a=>{M.value=a.data}),ga().then(a=>{K.value=a.data}),(async()=>va().then(a=>{j.value=a.data.regions,B.value=a.data.seriesData}))(),Y(),X()}catch{}};ba(()=>{Q()});const F=s([]),G=s([]),X=async()=>{const a=await da({});F.value=a.data.businessTypeCount,G.value=a.data.eventCount},Y=async()=>{const a=await ca({});n.value=a.data},n=s([]),Z=({rowIndex:a,columnIndex:t})=>{if(a===n.value.length)return[1,1];if(t===0){if(a===0||n.value[a].region!==n.value[a-1].region){let r=1;for(let u=a+1;u<n.value.length;u++)n.value[u].region===n.value[a].region&&r++;return[r,1]}return[0,0]}if(t===1){if(a===0||n.value[a].regioTown!==n.value[a-1].regioTown){let r=1;for(let u=a+1;u<n.value.length;u++)n.value[u].regioTown===n.value[a].regioTown&&r++;return[r,1]}return[0,0]}return[1,1]},aa=({columns:a})=>a.map((r,u)=>u===0?"\u603B\u8BA1":["eventCount","effectiveEventCount"].includes(r.property)?`${n.value.reduce((g,h)=>h.businessType!=="\u5408\u8BA1"?g+parseInt(h[r.property]):g,0)}`:"-");return(a,t)=>{const r=d("el-table-column"),u=d("el-table"),g=d("Postcard"),h=d("el-icon"),ea=d("el-pagination"),ta=d("el-card"),la=d("pane"),na=d("splitpanes"),ra=wa("loading");return f(),y("div",b,[l(na,null,{default:c(()=>[l(la,{class:"ml8"},{default:c(()=>[l(ta,null,{default:c(()=>[t[10]||(t[10]=e("div",{class:"top-title"},"\u5DE1\u68C0\u62A5\u544A",-1)),e("div",w,[e("div",x,[t[2]||(t[2]=e("h4",null,"\u822A\u7EBF\u6267\u98DE\u60C5\u51B5",-1)),e("div",C,[l(u,{data:n.value,border:"","show-summary":"","summary-method":aa,"span-method":Z,style:{width:"100%"}},{default:c(()=>[l(r,{align:"center",prop:"region",label:"\u533A\u57DF",width:"180"}),l(r,{align:"center",prop:"regioTown",label:"\u8857\u9053",width:"180"}),l(r,{align:"center",prop:"businessType",label:"\u573A\u666F"}),l(r,{align:"center",prop:"eventCount",label:"\u4E8B\u4EF6\u6570"}),l(r,{align:"center",prop:"effectiveEventCount",label:"\u6709\u6548\u4E8B\u4EF6\u6570"})]),_:1},8,["data"])])]),e("div",z,[t[3]||(t[3]=e("h4",null,"\u573A\u666F\u4E8B\u4EF6\u6570\u91CF\u7EDF\u8BA1\u56FE",-1)),e("div",T,[l(ia,{showLegend:!1,"inner-data":F.value,"outer-data":G.value},null,8,["inner-data","outer-data"])])]),e("div",k,[t[4]||(t[4]=e("h4",null,"\u793E\u533A\u4E8B\u4EF6\u6570\u91CFTOP10",-1)),e("div",E,[l(oa,{title:"","x-axis-data":j.value,"series-data":B.value},null,8,["x-axis-data","series-data"])])]),e("div",P,[t[9]||(t[9]=e("h4",null,"\u4E8B\u4EF6\u660E\u7EC6",-1)),(f(!0),y(xa,null,Ca(U.value,(i,R)=>za((f(),y("div",{"element-loading-text":"\u52A0\u8F7D\u4E2D...",key:R},[e("div",D,[e("table",null,[e("tr",null,[e("td",I,v((o.value.current-1)*o.value.size+R+1),1),t[5]||(t[5]=e("th",null,"\u4E8B\u4EF6",-1)),e("td",null,v(i.businessEventName),1),t[6]||(t[6]=e("th",null,"\u4E61\u9547",-1)),e("td",null,[Ta(v(i.lastCityName)+" ",1),l(h,{style:{float:"right",cursor:"pointer"},onClick:Ia=>{return V=i,void p.value.showDialog(V,1);var V},size:"20",color:"blue"},{default:c(()=>[l(g)]),_:2},1032,["onClick"])])]),e("tr",null,[t[7]||(t[7]=e("th",null,"\u793E\u533A",-1)),e("td",null,v(i.cityName),1),t[8]||(t[8]=e("th",null,"\u65F6\u95F4",-1)),e("td",N,v(i.time),1)]),e("tr",null,[e("th",S,[e("img",{src:i.picturePath,width:"800"},null,8,q)])])])])])),[[ra,m.value]])),128)),e("div",L,[l(ea,{"current-page":o.value.current,"onUpdate:currentPage":t[0]||(t[0]=i=>o.value.current=i),"page-size":o.value.size,"onUpdate:pageSize":t[1]||(t[1]=i=>o.value.size=i),"page-sizes":[10,20,30,40],small:"",background:"",layout:"total, sizes, prev, pager, next, jumper",total:$.value,onSizeChange:A,onCurrentChange:H},null,8,["current-page","page-size","total"])])])])]),_:1}),l(ma,{ref_key:"verifyDialogRef",ref:p,"close-dialog":J},null,512)]),_:1})]),_:1})])}}}),W=ka(O,[["__scopeId","data-v-bc984e96"]])});export{Pa as __tla,W as default};
