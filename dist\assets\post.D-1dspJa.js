import{s as n,__tla as h}from"./index.BSP3cg_z.js";let u,d,i,l,m,p,f,c,_=Promise.all([(()=>{try{return h}catch{}})()]).then(async()=>{l=function(t){return n({url:"/admin/post/page",method:"get",params:t})},p=t=>n({url:"/admin/post/list",method:"get",params:t}),u=function(t){return n({url:"/admin/post",method:"post",data:t})},m=function(t){return n({url:"/admin/post/details/"+t,method:"get"})};function s(t){return n({url:"/admin/post/details",method:"get",params:t})}i=function(t){return n({url:"/admin/post",method:"delete",data:t})},f=function(t){return n({url:"/admin/post",method:"put",data:t})},c=function(t,r,a,e){if(e)return a();s({postName:r}).then(o=>{o.data!==null?a(new Error("\u5C97\u4F4D\u540D\u79F0\u5DF2\u7ECF\u5B58\u5728")):a()})},d=function(t,r,a,e){if(e)return a();s({postCode:r}).then(o=>{o.data!==null?a(new Error("\u5C97\u4F4D\u7F16\u7801\u5DF2\u7ECF\u5B58\u5728")):a()})}});export{_ as __tla,u as a,d as b,i as d,l as f,m as g,p as l,f as p,c as v};
