import{a3 as fa,a4 as Wa,a5 as Xa,a6 as pe,a7 as Ya,a2 as P,E as h,a8 as ga,D as Za,a9 as ya,q as et,__tla as at}from"./index.BSP3cg_z.js";import tt,{__tla as lt}from"./TaskDetailDialog.SUU_PRaK.js";import st,{__tla as it}from"./MapPreviewDialog.DE0XHFE2.js";import nt,{__tla as rt}from"./AdvancedFilterDialog.74iVPqgf.js";import ut,{__tla as ot}from"./MapCompareDialog.SX4L_GRP.js";import{d as ka,k as u,c as Y,o as ct,S as dt,B as g,a as b,b as d,f as o,e as w,D as C,t as l,E as v,G as y,F as _e,v as n,u as $,p as ha,g as vt}from"./vue.CnN__PXn.js";let wa,mt=Promise.all([(()=>{try{return at}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return ot}catch{}})()]).then(async()=>{let fe,ge,ye,ke,he,we,be,Ce,$e,De,Te,Ie,ze,Ve,xe,Ue,Ee,Fe,Le,Re,Be,Se,Ae,Me,qe,Ne,Oe,Ge,Pe,He,je,Je;fe={class:"odm-task-monitor"},ge={class:"header"},ye={class:"actions"},ke={key:0,class:"refresh-info"},he={class:"page-header"},we={class:"filter-bar"},be={class:"search-area"},Ce={key:0,class:"right-section"},$e={class:"task-count"},De={key:0,class:"loading-container"},Te={key:1,class:"error-container"},Ie={key:2,class:"empty-container"},ze={key:3,class:"task-cards"},Ve={class:"card-header"},xe={class:"title-with-actions"},Ue={class:"task-status"},Ee={class:"task-duration"},Fe={class:"task-info"},Le={class:"task-times"},Re={key:0},Be={class:"process-timeline"},Se={class:"process-item"},Ae={class:"process-name"},Me={class:"process-status"},qe={key:0,class:"process-duration"},Ne={class:"batch-reset-dialog"},Oe={class:"filter-section"},Ge={class:"task-list-section"},Pe={class:"list-header"},He={class:"task-count"},je={class:"dialog-footer"},Je=ka({name:"onedragon"}),wa=et(ka({...Je,setup(pt){const B=u(!1),S=u(""),x=u([]),Ke=u(0),U=u(1),Z=u(10),E=u(""),F=u(!0),H=u(null),j=u(null),ee=u(null),A=u(60),ae=u(!1),Qe=u(""),We=u([]),Xe=u(""),Ye=u(""),te=u(!1),Ze=u(""),le=u(!1),D=u({status:[],timeRange:null,sortField:"default_priority",sortOrder:"ascending"}),J=u(!1),se=u(""),K=u({}),M=u(!1),Q=u(!1),I=u([]),ea=u(!1),aa=u(!1),T=u({status:[],taskId:""}),ta=u([]),ba=e=>{e?(la(),W()):(ie(),ne())},la=()=>{ie(),H.value=window.setInterval(()=>{z()},6e4)},ie=()=>{H.value!==null&&(clearInterval(H.value),H.value=null)},W=()=>{ne(),A.value=60,j.value=window.setInterval(()=>{A.value>0?A.value--:z()},1e3)},ne=()=>{j.value!==null&&(clearInterval(j.value),j.value=null)},Ca=e=>{if(!e)return"";const a=new Date().getTime()-e.getTime();return a<6e4?`${Math.floor(a/1e3)}\u79D2\u524D`:a<36e5?`${Math.floor(a/6e4)}\u5206\u949F\u524D`:e.toLocaleTimeString("zh-CN")},sa=Y(()=>D.value.status.length>0||D.value.timeRange!==null||E.value.trim()!==""),q=Y(()=>{let e=[...x.value];if(E.value.trim()){const r=E.value.toLowerCase().trim();e=e.filter(i=>i.task_id.toLowerCase().includes(r))}if(D.value.status.length>0&&(e=e.filter(r=>{const i=f(r.status);return D.value.status.includes(i)})),D.value.timeRange){const[r,i]=D.value.timeRange,c=new Date(r).getTime(),k=new Date(i+" 23:59:59").getTime();e=e.filter(m=>{const p=new Date(m.start_time).getTime();return p>=c&&p<=k})}const{sortField:a,sortOrder:s}=D.value;return e=e.sort((r,i)=>{let c,k;switch(a){case"default_priority":const m=oa(r.status),p=oa(i.status);return m!==p?s==="ascending"?m-p:p-m:r.task_id.localeCompare(i.task_id);case"task_id":default:c=r.task_id,k=i.task_id;break;case"status":c=f(r.status),k=f(i.status);break;case"start_time":c=r.start_time?new Date(r.start_time).getTime():0,k=i.start_time?new Date(i.start_time).getTime():0;break;case"end_time":c=r.end_time?new Date(r.end_time).getTime():1/0,k=i.end_time?new Date(i.end_time).getTime():1/0;break;case"duration":c=ia(r.start_time,r.end_time),k=ia(i.start_time,i.end_time)}return a==="default_priority"?0:s==="ascending"?c>k?1:-1:c<k?1:-1}),e}),ia=(e,a)=>{if(!e)return 0;if(!a)return 1/0;try{const s=new Date(e).getTime();return new Date(a).getTime()-s}catch{return 0}},$a=Y(()=>{const e=(U.value-1)*Z.value,a=e+Z.value;return q.value.slice(e,a)}),re=Y(()=>{let e=ta.value.filter(a=>f(a.status)!=="\u672A\u5F00\u59CB");if(T.value.status.length>0&&(e=e.filter(a=>{const s=f(a.status);return T.value.status.includes(s)})),T.value.taskId.trim()){const a=T.value.taskId.trim().toLowerCase();e=e.filter(s=>s.task_id.toLowerCase().includes(a))}return e}),Da=()=>{U.value=1},z=async()=>{B.value=!0,S.value="";try{const e=await P.get("http://192.168.43.148:8091/api/map/odm/tasks");if(e.data.status!=="success")throw new Error(e.data.message||"\u8BF7\u6C42\u5931\u8D25");x.value=e.data.tasks||[],Ke.value=e.data.count||0,ee.value=new Date,F.value&&W()}catch(e){S.value=e instanceof Error?e.message:"\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",h.error("\u83B7\u53D6\u4EFB\u52A1\u6570\u636E\u5931\u8D25")}finally{B.value=!1}},na=()=>x.value.some(e=>f(e.status)==="\u8FDB\u884C\u4E2D"),Ta=async e=>{var r;const a=e.task_id,s=f(e.status)==="\u8FDB\u884C\u4E2D"||!na();if(!K.value[a]){K.value[a]=!0;try{const i="192.168.43.148",c="8091",k=`http://${i}:${c}/api/map/odm/task/rename-info`,m=await P.get(k,{params:{task_id:a}});if(s){const p=`http://${i}:${c}/api/map/odm/reset-task-config`;await P.get(p)}if(!m.data||m.data.status!=="success")throw new Error(((r=m.data)==null?void 0:r.message)||"\u4EFB\u52A1\u91CD\u7F6E\u5931\u8D25");h.success("\u4EFB\u52A1\u5DF2\u91CD\u7F6E"),z()}catch(i){h.error(i instanceof Error?i.message:"\u4EFB\u52A1\u91CD\u7F6E\u5931\u8D25")}finally{K.value[a]=!1}}},Ia=async()=>{M.value=!0,await ue()},ue=async()=>{try{await z(),ta.value=[...x.value],I.value=[],ea.value=!1,aa.value=!1}catch{h.error("\u5237\u65B0\u5217\u8868\u5931\u8D25")}},za=()=>{T.value={status:[],taskId:""}},Va=e=>{I.value=e;const a=re.value.length,s=e.length;ea.value=s===a&&a>0,aa.value=s>0&&s<a},xa=()=>{I.value.length!==0?ga.confirm(`\u786E\u5B9A\u8981\u91CD\u7F6E\u9009\u4E2D\u7684 ${I.value.length} \u4E2A\u4EFB\u52A1\u5417\uFF1F`,"\u6279\u91CF\u91CD\u7F6E\u4EFB\u52A1",{confirmButtonText:"\u786E\u5B9A\u91CD\u7F6E",cancelButtonText:"\u53D6\u6D88",type:"warning",confirmButtonClass:"el-button--danger"}).then(()=>{Ua()}).catch(()=>{h.info("\u5DF2\u53D6\u6D88\u6279\u91CF\u91CD\u7F6E")}):h.warning("\u8BF7\u9009\u62E9\u8981\u91CD\u7F6E\u7684\u4EFB\u52A1")},Ua=async()=>{var e;if(!Q.value){Q.value=!0;try{const a="192.168.43.148",s="8091";let r=0,i=0;const c=[],k=I.value.some(m=>f(m.status)==="\u8FDB\u884C\u4E2D")||!na();for(const m of I.value)try{const p=`http://${a}:${s}/api/map/odm/task/rename-info`,N=await P.get(p,{params:{task_id:m.task_id}});N.data&&N.data.status==="success"?r++:(i++,c.push(`\u4EFB\u52A1 ${m.task_id}: ${((e=N.data)==null?void 0:e.message)||"\u91CD\u7F6E\u5931\u8D25"}`))}catch(p){i++,c.push(`\u4EFB\u52A1 ${m.task_id}: ${p instanceof Error?p.message:"\u91CD\u7F6E\u5931\u8D25"}`)}if(k&&r>0)try{const m=`http://${a}:${s}/api/map/odm/reset-task-config`;await P.get(m)}catch{h.warning("\u4EFB\u52A1\u91CD\u7F6E\u6210\u529F\uFF0C\u4F46\u914D\u7F6E\u91CD\u7F6E\u5931\u8D25")}r>0&&i===0?h.success(`\u6210\u529F\u91CD\u7F6E ${r} \u4E2A\u4EFB\u52A1`):r>0&&i>0?h.warning(`\u6210\u529F\u91CD\u7F6E ${r} \u4E2A\u4EFB\u52A1\uFF0C${i} \u4E2A\u4EFB\u52A1\u91CD\u7F6E\u5931\u8D25`):h.error("\u6240\u6709\u4EFB\u52A1\u91CD\u7F6E\u5931\u8D25"),await z(),await ue(),i===0&&(M.value=!1)}catch{h.error("\u6279\u91CF\u91CD\u7F6E\u4EFB\u52A1\u5931\u8D25")}finally{Q.value=!1}}},L=e=>{if(!e)return"\u6682\u65E0";if(e.includes("-")&&e.includes(":"))return e;try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")}catch{return e||"\u6682\u65E0"}},ra=(e,a)=>{if(!e||!a)return"\u8FDB\u884C\u4E2D";try{const s=new Date(e).getTime(),r=new Date(a).getTime()-s,i=Math.floor(r/6e4),c=Math.floor(r%6e4/1e3);return i>60?`${Math.floor(i/60)}\u5C0F\u65F6${i%60}\u5206${c}\u79D2`:`${i}\u5206${c}\u79D2`}catch{return"\u8BA1\u7B97\u9519\u8BEF"}},Ea=(e,a)=>{const s=e?L(e):"\u672A\u5F00\u59CB",r=a?L(a):"\u8FDB\u884C\u4E2D";return a?`${s} \u81F3 ${r}`:s},Fa=e=>e.details?{odm_process:e.details.odm_process||{status:"\u7B49\u5F85\u5F00\u59CB"},tif_process:e.details.tif_process||{status:"\u7B49\u5F85\u5F00\u59CB"},geoserver_publish:e.details.geoserver_publish||{status:"\u7B49\u5F85\u5F00\u59CB"},map_config:e.details.map_config||{status:"\u7B49\u5F85\u5F00\u59CB"}}:{},ua=e=>({odm_process:"\u5F71\u50CF\u62FC\u63A5",tif_process:"\u5F71\u50CF\u9884\u5904\u7406",geoserver_publish:"\u53D1\u5E03\u5F71\u50CF",map_config:"\u5199\u5165\u5730\u56FE\u914D\u7F6E"})[e]||e,oe=e=>{if(f(e)==="\u8FDB\u884C\u4E2D")return"primary";switch(e){case"\u5B8C\u6210":return"success";case"\u5931\u8D25":return"danger";case"\u7B49\u5F85\u5F00\u59CB":case"\u672A\u5F00\u59CB":return"info";default:return"primary"}},f=e=>e==="\u5B8C\u6210"||e==="\u7B49\u5F85\u5F00\u59CB"||e==="\u672A\u5F00\u59CB"||e==="\u5931\u8D25"?e:"\u8FDB\u884C\u4E2D",oa=e=>{switch(f(e)){case"\u8FDB\u884C\u4E2D":return 1;case"\u672A\u5F00\u59CB":return 2;case"\u5B8C\u6210":return 3;case"\u5931\u8D25":return 4;default:return 5}},La=e=>{if(f(e)==="\u8FDB\u884C\u4E2D")return"primary";switch(e){case"\u5B8C\u6210":return"success";case"\u5931\u8D25":return"danger";case"\u7B49\u5F85\u5F00\u59CB":case"\u672A\u5F00\u59CB":return"info";default:return""}},Ra=e=>{if(f(e)==="\u8FDB\u884C\u4E2D")return"#409EFF";switch(e){case"\u5B8C\u6210":return"#67C23A";case"\u5931\u8D25":return"#F56C6C";case"\u7B49\u5F85\u5F00\u59CB":case"\u672A\u5F00\u59CB":return"#909399";default:return""}},Ba=e=>{U.value=e},Sa=()=>{z(),F.value&&W()},ca=e=>{Ze.value=e,te.value=!0},Aa=()=>{le.value=!0},Ma=e=>{D.value={...e},U.value=1},qa=()=>{var a,s,r,i;const e=x.value.find(c=>c.status==="\u5B8C\u6210");e&&(se.value=((s=(a=e.details)==null?void 0:a.geoserver_publish)==null?void 0:s.layer_name)||((i=(r=e.details)==null?void 0:r.map_config)==null?void 0:i.layer_id)||""),J.value=!0},Na=()=>{E.value="",D.value={status:[],timeRange:null,sortField:"default_priority",sortOrder:"ascending"},U.value=1},da=e=>{se.value=e,J.value=!0};return ct(()=>{z(),F.value&&(la(),W())}),dt(()=>{ie(),ne()}),(e,a)=>{const s=g("el-button"),r=g("el-switch"),i=g("el-input"),c=g("el-tag"),k=g("el-skeleton"),m=g("el-empty"),p=g("el-tooltip"),N=g("el-icon"),Oa=g("el-timeline-item"),Ga=g("el-timeline"),Pa=g("el-card"),Ha=g("el-pagination"),ce=g("el-option"),ja=g("el-select"),de=g("el-col"),Ja=g("el-row"),O=g("el-table-column"),Ka=g("el-table"),Qa=g("el-dialog");return d(),b("div",fe,[o("div",ge,[a[11]||(a[11]=o("h2",null,"\u65E0\u4EBA\u673A\u5F71\u50CF\u5904\u7406\u4EFB\u52A1\u76D1\u63A7",-1)),o("div",ye,[ee.value?(d(),b("span",ke,[v(" \u4E0A\u6B21\u5237\u65B0: "+y(Ca(ee.value))+" ",1),F.value&&A.value>0?(d(),b(_e,{key:0},[v(" | \u4E0B\u6B21\u5237\u65B0: "+y(A.value)+"\u79D2 ",1)],64)):C("",!0)])):C("",!0),l(s,{type:"primary",icon:$(fa),onClick:Sa,loading:B.value},{default:n(()=>a[10]||(a[10]=[v("\u5237\u65B0\u6570\u636E")])),_:1},8,["icon","loading"]),l(r,{modelValue:F.value,"onUpdate:modelValue":a[0]||(a[0]=t=>F.value=t),"active-text":"\u81EA\u52A8\u5237\u65B0","inactive-text":"",onChange:ba},null,8,["modelValue"])])]),o("div",he,[o("div",we,[o("div",be,[l(i,{modelValue:E.value,"onUpdate:modelValue":a[1]||(a[1]=t=>E.value=t),placeholder:"\u8F93\u5165\u4EFB\u52A1ID\u641C\u7D22",clearable:"","prefix-icon":"Search",style:{width:"300px"},onInput:Da},null,8,["modelValue"]),sa.value?(d(),w(s,{key:1,type:"warning",icon:$(Xa),onClick:Na},{default:n(()=>a[13]||(a[13]=[v("\u53D6\u6D88\u7B5B\u9009")])),_:1},8,["icon"])):(d(),w(s,{key:0,type:"primary",icon:$(Wa),onClick:Aa},{default:n(()=>a[12]||(a[12]=[v("\u9AD8\u7EA7\u7B5B\u9009")])),_:1},8,["icon"])),l(s,{type:"success",icon:$(pe),onClick:qa},{default:n(()=>a[14]||(a[14]=[v("\u56FE\u5C42\u6BD4\u8F83")])),_:1},8,["icon"])])]),B.value||S.value?C("",!0):(d(),b("div",Ce,[l(s,{type:"warning",icon:$(Ya),onClick:Ia},{default:n(()=>a[15]||(a[15]=[v("\u6279\u91CF\u91CD\u7F6E")])),_:1},8,["icon"]),o("div",$e,[a[17]||(a[17]=v(" \u5171 ")),o("strong",null,y(Ke.value),1),a[18]||(a[18]=v(" \u4E2A\u4EFB\u52A1\uFF0C\u5F53\u524D\u663E\u793A ")),o("strong",null,y(q.value.length),1),a[19]||(a[19]=v(" \u4E2A ")),sa.value?(d(),w(c,{key:0,size:"small",type:"info"},{default:n(()=>a[16]||(a[16]=[v("\u5DF2\u5E94\u7528\u7B5B\u9009")])),_:1})):C("",!0)])]))]),B.value?(d(),b("div",De,[l(k,{rows:5,animated:""}),l(k,{rows:5,animated:"",style:{"margin-top":"20px"}})])):S.value?(d(),b("div",Te,[l(m,{description:"\u6570\u636E\u52A0\u8F7D\u5931\u8D25"},{description:n(()=>[o("p",null,y(S.value),1)]),default:n(()=>[l(s,{type:"primary",onClick:z},{default:n(()=>a[20]||(a[20]=[v("\u91CD\u8BD5")])),_:1})]),_:1})])):q.value.length===0?(d(),b("div",Ie,[l(m,{description:x.value.length>0?"\u6CA1\u6709\u7B26\u5408\u6761\u4EF6\u7684\u4EFB\u52A1":"\u6682\u65E0\u4EFB\u52A1\u6570\u636E"},null,8,["description"])])):(d(),b("div",ze,[(d(!0),b(_e,null,ha($a.value,t=>{return d(),w(Pa,{key:t.task_id,class:vt(["task-card",(ve=t.status,f(ve)==="\u8FDB\u884C\u4E2D"?"task-status-\u5904\u7406\u4E2D":`task-status-${ve}`)])},{header:n(()=>{var _,R,me,V,G,va,ma,pa;return[o("div",Ve,[o("div",xe,[o("h3",null,"\u4EFB\u52A1ID: "+y(t.task_id),1),t.status!=="\u672A\u5F00\u59CB"?(d(),w(p,{key:0,content:"\u91CD\u7F6E\u4EFB\u52A1",placement:"top"},{default:n(()=>[l(s,{type:"info",size:"small",circle:"",icon:$(fa),onClick:X=>(_a=>{ga.confirm(`\u786E\u5B9A\u8981\u91CD\u7F6E\u4EFB\u52A1 ${_a.task_id} \u5417\uFF1F`,"\u91CD\u7F6E\u4EFB\u52A1",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}).then(()=>{Ta(_a)}).catch(()=>{h.info("\u5DF2\u53D6\u6D88\u91CD\u7F6E")})})(t),loading:K.value[t.task_id]},null,8,["icon","onClick","loading"])]),_:2},1024)):C("",!0)]),o("div",Ue,[t.status==="\u5B8C\u6210"&&((R=(_=t.details)==null?void 0:_.geoserver_publish)!=null&&R.layer_name)?(d(),w(p,{key:0,content:"\u56FE\u5C42\u6BD4\u8F83",placement:"top"},{default:n(()=>[l(s,{type:"primary",size:"small",circle:"",icon:$(pe),onClick:X=>da(t.details.geoserver_publish.layer_name)},null,8,["icon","onClick"])]),_:2},1024)):t.status==="\u5B8C\u6210"&&((V=(me=t.details)==null?void 0:me.map_config)!=null&&V.layer_id)?(d(),w(p,{key:1,content:"\u56FE\u5C42\u6BD4\u8F83",placement:"top"},{default:n(()=>[l(s,{type:"primary",size:"small",circle:"",icon:$(pe),onClick:X=>da(t.details.map_config.layer_id)},null,8,["icon","onClick"])]),_:2},1024)):C("",!0),t.status==="\u5B8C\u6210"&&((va=(G=t.details)==null?void 0:G.geoserver_publish)!=null&&va.layer_name)?(d(),w(p,{key:2,content:"\u9884\u89C8\u56FE\u5C42",placement:"top"},{default:n(()=>[l(s,{type:"success",size:"small",circle:"",icon:$(ya),onClick:X=>ca(t.details.geoserver_publish.layer_name)},null,8,["icon","onClick"])]),_:2},1024)):t.status==="\u5B8C\u6210"&&((pa=(ma=t.details)==null?void 0:ma.map_config)!=null&&pa.layer_id)?(d(),w(p,{key:3,content:"\u9884\u89C8\u56FE\u5C42",placement:"top"},{default:n(()=>[l(s,{type:"success",size:"small",circle:"",icon:$(ya),onClick:X=>ca(t.details.map_config.layer_id)},null,8,["icon","onClick"])]),_:2},1024)):C("",!0),l(c,{type:f(t.status)==="\u8FDB\u884C\u4E2D"?"primary":oe(t.status)},{default:n(()=>[v(y(f(t.status)),1)]),_:2},1032,["type"]),o("span",Ee,"\u8017\u65F6: "+y(ra(t.start_time,t.end_time)),1)])])]}),default:n(()=>[o("div",Fe,[o("div",Le,[o("p",null,[a[21]||(a[21]=o("i",{class:"el-icon-time"},null,-1)),v(" \u5F00\u59CB\u65F6\u95F4: "+y(L(t.start_time)),1)]),t.end_time?(d(),b("p",Re,[a[22]||(a[22]=o("i",{class:"el-icon-time"},null,-1)),v(" \u7ED3\u675F\u65F6\u95F4: "+y(L(t.end_time)),1)])):C("",!0)]),o("div",Be,[l(Ga,null,{default:n(()=>[(d(!0),b(_e,null,ha(Fa(t),(_,R)=>(d(),w(Oa,{key:R,type:La(_.status),color:Ra(_.status),timestamp:Ea(_.start_time,_.end_time),hollow:_.status!=="\u5904\u7406\u4E2D",size:_.status==="\u5904\u7406\u4E2D"?"large":"normal"},{default:n(()=>[o("div",Se,[o("div",Ae,[v(y(ua(R))+" ",1),_.retry_count&&_.retry_count>0?(d(),w(c,{key:0,size:"small",effect:"dark",type:"warning"},{default:n(()=>[v(" \u91CD\u8BD5"+y(_.retry_count)+"\u6B21 ",1)]),_:2},1024)):C("",!0),_.task_id?(d(),w(p,{key:1,content:"\u67E5\u770B\u5904\u7406\u65E5\u5FD7",placement:"top"},{default:n(()=>[l(s,{type:"primary",size:"small",circle:"",class:"query-task-btn",onClick:me=>((V,G)=>{V.task_id?(Qe.value=V.task_id,We.value=Array.isArray(V.task_list)?V.task_list:[V.task_id],Xe.value=ua(G),Ye.value=G,ae.value=!0):h.warning("\u5F53\u524D\u4EFB\u52A1\u6CA1\u6709\u53EF\u67E5\u8BE2\u7684ID")})(_,R)},{default:n(()=>[l(N,null,{default:n(()=>[l($(Za))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)):C("",!0)]),o("div",Me,[l(c,{size:"small",type:f(_.status)==="\u8FDB\u884C\u4E2D"?"primary":oe(_.status)},{default:n(()=>[v(y(f(_.status)),1)]),_:2},1032,["type"]),_.status==="\u5B8C\u6210"?(d(),b("span",qe," \u8017\u65F6: "+y(ra(_.start_time,_.end_time)),1)):C("",!0)])])]),_:2},1032,["type","color","timestamp","hollow","size"]))),128))]),_:2},1024)])])]),_:2},1032,["class"]);var ve}),128))])),q.value.length>0?(d(),w(Ha,{key:4,background:"",layout:"prev, pager, next",total:q.value.length,"page-size":Z.value,"current-page":U.value,onCurrentChange:Ba,class:"pagination"},null,8,["total","page-size","current-page"])):C("",!0),l(tt,{visible:ae.value,"onUpdate:visible":a[2]||(a[2]=t=>ae.value=t),"task-id":Qe.value,"task-list":We.value,"process-name":Xe.value,"process-type":Ye.value},null,8,["visible","task-id","task-list","process-name","process-type"]),l(st,{visible:te.value,"onUpdate:visible":a[3]||(a[3]=t=>te.value=t),"layer-id":Ze.value},null,8,["visible","layer-id"]),l(nt,{visible:le.value,"onUpdate:visible":a[4]||(a[4]=t=>le.value=t),"filter-settings":D.value,onApplyFilters:Ma},null,8,["visible","filter-settings"]),l(ut,{visible:J.value,"onUpdate:visible":a[5]||(a[5]=t=>J.value=t),"initial-layer-id":se.value},null,8,["visible","initial-layer-id"]),l(Qa,{modelValue:M.value,"onUpdate:modelValue":a[9]||(a[9]=t=>M.value=t),title:"\u6279\u91CF\u91CD\u7F6E\u4EFB\u52A1",width:"85%",top:"5vh","close-on-click-modal":!1,"destroy-on-close":""},{footer:n(()=>[o("div",je,[l(s,{onClick:a[8]||(a[8]=t=>M.value=!1)},{default:n(()=>a[25]||(a[25]=[v("\u53D6\u6D88")])),_:1}),l(s,{type:"danger",onClick:xa,disabled:I.value.length===0,loading:Q.value},{default:n(()=>[v(" \u91CD\u7F6E\u9009\u4E2D\u4EFB\u52A1 ("+y(I.value.length)+") ",1)]),_:1},8,["disabled","loading"])])]),default:n(()=>[o("div",Ne,[o("div",Oe,[l(Ja,{gutter:16},{default:n(()=>[l(de,{span:8},{default:n(()=>[l(ja,{modelValue:T.value.status,"onUpdate:modelValue":a[6]||(a[6]=t=>T.value.status=t),placeholder:"\u7B5B\u9009\u4EFB\u52A1\u72B6\u6001",clearable:"",multiple:"","collapse-tags":"",style:{width:"100%"}},{default:n(()=>[l(ce,{label:"\u8FDB\u884C\u4E2D",value:"\u8FDB\u884C\u4E2D"}),l(ce,{label:"\u5B8C\u6210",value:"\u5B8C\u6210"}),l(ce,{label:"\u5931\u8D25",value:"\u5931\u8D25"})]),_:1},8,["modelValue"])]),_:1}),l(de,{span:8},{default:n(()=>[l(i,{modelValue:T.value.taskId,"onUpdate:modelValue":a[7]||(a[7]=t=>T.value.taskId=t),placeholder:"\u641C\u7D22\u4EFB\u52A1ID",clearable:"","prefix-icon":"Search"},null,8,["modelValue"])]),_:1}),l(de,{span:8},{default:n(()=>[l(s,{type:"primary",onClick:ue},{default:n(()=>a[23]||(a[23]=[v("\u5237\u65B0\u5217\u8868")])),_:1}),l(s,{onClick:za},{default:n(()=>a[24]||(a[24]=[v("\u6E05\u7A7A\u7B5B\u9009")])),_:1})]),_:1})]),_:1})]),o("div",Ge,[o("div",Pe,[o("span",He," \u5171 "+y(re.value.length)+" \u4E2A\u53EF\u91CD\u7F6E\u4EFB\u52A1 ",1)]),l(Ka,{ref:"batchResetTable",data:re.value,style:{width:"100%"},height:"500",onSelectionChange:Va},{default:n(()=>[l(O,{type:"selection",width:"55"}),l(O,{prop:"task_id",label:"\u4EFB\u52A1ID","min-width":"250"}),l(O,{prop:"status",label:"\u72B6\u6001",width:"120"},{default:n(({row:t})=>[l(c,{type:oe(f(t.status))},{default:n(()=>[v(y(f(t.status)),1)]),_:2},1032,["type"])]),_:1}),l(O,{prop:"start_time",label:"\u5F00\u59CB\u65F6\u95F4","min-width":"200"},{default:n(({row:t})=>[v(y(L(t.start_time)),1)]),_:1}),l(O,{prop:"end_time",label:"\u7ED3\u675F\u65F6\u95F4","min-width":"200"},{default:n(({row:t})=>[v(y(t.end_time?L(t.end_time):"\u8FDB\u884C\u4E2D"),1)]),_:1})]),_:1},8,["data"])])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-27674b49"]])});export{mt as __tla,wa as default};
