import{s as e,__tla as i}from"./index.BSP3cg_z.js";let a,n,s,r,u,d=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{r=function(t){return e({url:"/admin/businessAuditDepartment/page",method:"get",params:t})},n=t=>e({url:"/admin/businessAuditDepartment/getAuditAreaList",method:"get",params:t}),a=function(t){return e({url:"/admin/businessAuditDepartment",method:"post",data:t})},s=function(t){return e({url:"/admin/businessAuditDepartment",method:"delete",data:t})},u=function(t){return e({url:"/admin/businessAuditDepartment",method:"put",data:t})}});export{d as __tla,a,n as b,s as d,r as f,u as p};
