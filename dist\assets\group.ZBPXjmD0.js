import{s as r,__tla as l}from"./index.BSP3cg_z.js";let e,n,u,a,o,g,s=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{u=function(t){return r({url:"/gen/group/page",method:"get",params:t})},e=function(t){return r({url:"/gen/group",method:"post",data:t})},a=function(t){return r({url:"/gen/group/"+t,method:"get"})},n=function(t){return r({url:"/gen/group",method:"delete",data:t})},g=function(t){return r({url:"/gen/group",method:"put",data:t})},o=function(){return r({url:"/gen/group/list",method:"get"})}});export{s as __tla,e as a,n as d,u as f,a as g,o as l,g as p};
