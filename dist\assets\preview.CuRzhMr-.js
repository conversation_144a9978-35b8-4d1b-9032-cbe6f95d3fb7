const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.BQuhesfM.js","assets/vue.CnN__PXn.js","assets/index.BSP3cg_z.js","assets/index.DjeikzAe.css","assets/index.BnAUR8RY.css"])))=>i.map(i=>d[i]);
import{a as z,K as L,q,__tla as B}from"./index.BSP3cg_z.js";import{l as F,__tla as H}from"./table.BExdFBu3.js";import{c as K,__tla as S}from"./commonFunction.BmnVIZty.js";import{d as V,k as m,A as G,B as d,e as N,b as g,v as i,t as s,u as r,a as J,F as M,p as Q,j as W,H as x}from"./vue.CnN__PXn.js";let I,X=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return S}catch{}})()]).then(async()=>{let b,k;b=V({name:"preview"}),k=V({...b,setup(Y,{expose:T}){const C=W(()=>z(()=>import("./index.BQuhesfM.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4]))),{copyText:w}=K(),h=m(!1),p=G({open:!1,titel:"\u4EE3\u7801\u9884\u89C8",fileTree:[],activeName:""}),f=m([]),_=m(""),v=m([]),P=m(!1),A=m(),D=a=>{P.value=!0,v.value=[],F(a).then(e=>{f.value=e;for(let o in e)v.value.push(e[o].codePath);_.value=e[0].code,p.activeName=e[0].codePath;const l=R(v);p.fileTree=L(l,"id","parentId","children","/")}).finally(()=>{P.value=!1})},E=async(a,e)=>{if(e&&!e.isLeaf)return!1;p.activeName=a.id;const l=f.value.filter(o=>o.codePath===a.id);l.length>0&&(_.value=l[0].code)},O=a=>{const e=f.value.filter(l=>l.codePath===a.paneName);e.length>0&&(_.value=e[0].code)},R=a=>{const e={},l=[];for(const o of a.value){let c=[];c=o.includes("\\")?o.split("\\"):o.split("/");let n="";for(let u=0;u<c.length;u++){const y=n;n=n.length===0?c[u]:n.replaceAll(".","/")+"/"+c[u],e[n]||(e[n]=!0,l.push({id:n,label:c[u],parentId:y||"/",templateName:o.k}))}}return l};return T({openDialog:async a=>{await D(a),h.value=!0}}),(a,e)=>{const l=d("el-tree"),o=d("el-scrollbar"),c=d("pane"),n=d("SvgIcon"),u=d("el-tab-pane"),y=d("el-tabs"),U=d("splitpanes"),j=d("el-dialog");return g(),N(j,{fullscreen:"",title:"\u4EE3\u7801\u9884\u89C8",modelValue:r(h),"onUpdate:modelValue":e[2]||(e[2]=t=>x(h)?h.value=t:null),width:"90%",top:"3vh","append-to-body":"","close-on-click-modal":!1},{default:i(()=>[s(U,null,{default:i(()=>[s(c,{size:"25"},{default:i(()=>[s(o,{height:"calc(100vh - 100px)",class:"mt20"},{default:i(()=>[s(l,{ref:"treeRef","node-key":"id",data:r(p).fileTree,"expand-on-click-node":!1,"highlight-current":"",onNodeClick:E},null,8,["data"])]),_:1})]),_:1}),s(c,null,{default:i(()=>[s(y,{modelValue:r(p).activeName,"onUpdate:modelValue":e[1]||(e[1]=t=>r(p).activeName=t),onTabClick:O},{default:i(()=>[(g(!0),J(M,null,Q(r(f),t=>(g(),N(u,{label:t.codePath.substring(t.codePath.lastIndexOf("/")+1),name:t.codePath,key:t.codePath},{default:i(()=>[s(n,{name:"ele-CopyDocument",size:25,class:"copy_btn",onClick:Z=>r(w)(t.code)},null,8,["onClick"])]),_:2},1032,["label","name"]))),128)),s(r(C),{ref_key:"codeEditorRef",ref:A,theme:"darcula",modelValue:r(_),"onUpdate:modelValue":e[0]||(e[0]=t=>x(_)?_.value=t:null),mode:"go",readOnly:"",height:"calc(100vh - 100px)"},null,8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}}}),I=q(k,[["__scopeId","data-v-23448ba2"]])});export{X as __tla,I as default};
