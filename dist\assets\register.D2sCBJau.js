const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.8jUxxMvw.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/index.CFxn76eV.css"])))=>i.map(i=>d[i]);
import{v as R,r as z,a as C,c as w,__tla as D}from"./index.BSP3cg_z.js";import{v as G,b as N,r as O,__tla as H}from"./user.BGf96gmW.js";import{d as v,k as m,A as y,B as s,m as J,e as V,b as F,v as r,t as a,u as o,j as K,E as x,G as p,q as M,f as Q}from"./vue.CnN__PXn.js";let k,W=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let _;_=v({name:"register"}),k=v({..._,emits:["afterSuccess"],setup(X,{emit:P}){const U=P,$=K(()=>C(()=>import("./index.8jUxxMvw.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3,4]))),{t:q}=R.useI18n(),g=m(),c=m(!1),f=m("0"),t=y({isShowPassword:!1,ruleForm:{username:"",password:"",phone:"",checked:""}}),S=y({username:[{required:!0,message:"\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{min:5,max:20,message:"\u7528\u6237\u540D\u79F0\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 5 \u548C 20 \u4E4B\u95F4",trigger:"blur"},{validator:(e,l,u)=>{N(e,l,u,!1)},trigger:"blur"}],phone:[{required:!0,message:"\u624B\u673A\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:z.validatePhone,trigger:"blur"},{validator:(e,l,u)=>{G(e,l,u,!1)},trigger:"blur"}],password:[{required:!0,message:"\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{min:6,max:20,message:"\u7528\u6237\u5BC6\u7801\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 6 \u548C 20 \u4E4B\u95F4",trigger:"blur"},{validator:(e,l,u)=>{Number(f.value)<2?u("\u5BC6\u7801\u5F3A\u5EA6\u592A\u4F4E"):u()},trigger:"blur"}],checked:[{required:!0,message:"\u8BF7\u9605\u8BFB\u5E76\u540C\u610F\u6761\u6B3E",trigger:"blur"}]}),A=e=>{f.value=e},E=async()=>{if(!await g.value.validate().catch(()=>{}))return!1;try{c.value=!0,await O(t.ruleForm),w().success(q("common.optSuccessText")),U("afterSuccess")}catch(e){w().error(e.msg)}finally{c.value=!1}};return(e,l)=>{const u=s("ele-User"),d=s("el-icon"),h=s("el-input"),i=s("el-form-item"),L=s("ele-Unlock"),T=s("ele-Position"),j=s("el-checkbox"),b=s("el-button"),B=s("el-form"),I=J("waves");return F(),V(B,{size:"large",class:"login-content-form",rules:o(S),ref_key:"dataFormRef",ref:g,model:o(t).ruleForm},{default:r(()=>[a(i,{class:"login-animation1",prop:"username"},{default:r(()=>[a(h,{text:"",placeholder:e.$t("password.accountPlaceholder1"),modelValue:o(t).ruleForm.username,"onUpdate:modelValue":l[0]||(l[0]=n=>o(t).ruleForm.username=n),clearable:"",autocomplete:"off"},{prefix:r(()=>[a(d,{class:"el-input__icon"},{default:r(()=>[a(u)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),a(i,{class:"login-animation2",prop:"password"},{default:r(()=>[a(o($),{placeholder:e.$t("password.accountPlaceholder2"),modelValue:o(t).ruleForm.password,"onUpdate:modelValue":l[1]||(l[1]=n=>o(t).ruleForm.password=n),autocomplete:"off",maxLength:20,minLength:6,onScore:A},{prefix:r(()=>[a(d,{class:"el-input__icon"},{default:r(()=>[a(L)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),a(i,{class:"login-animation3",prop:"phone"},{default:r(()=>[a(h,{text:"",placeholder:e.$t("password.phonePlaceholder4"),modelValue:o(t).ruleForm.phone,"onUpdate:modelValue":l[2]||(l[2]=n=>o(t).ruleForm.phone=n),clearable:"",autocomplete:"off"},{prefix:r(()=>[a(d,{class:"el-input__icon"},{default:r(()=>[a(T)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),a(i,null,{default:r(()=>[a(j,{modelValue:o(t).ruleForm.checked,"onUpdate:modelValue":l[3]||(l[3]=n=>o(t).ruleForm.checked=n)},{default:r(()=>[x(p(e.$t("password.readAccept")),1)]),_:1},8,["modelValue"]),a(b,{link:"",type:"primary"},{default:r(()=>[x(p(e.$t("password.privacyPolicy")),1)]),_:1})]),_:1}),a(i,{class:"login-animation4"},{default:r(()=>[M((F(),V(b,{type:"primary",class:"login-content-submit",onClick:E,loading:o(c)},{default:r(()=>[Q("span",null,p(e.$t("password.registerBtnText")),1)]),_:1},8,["loading"])),[[I]])]),_:1})]),_:1},8,["rules","model"])}}})});export{W as __tla,k as default};
