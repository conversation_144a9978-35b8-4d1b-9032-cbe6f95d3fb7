const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.uFcdAvQi.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css"])))=>i.map(i=>d[i]);
import{a as x,__tla as y}from"./index.BSP3cg_z.js";import{d as i,k as m,B as _,e as k,b as D,v as t,t as d,u as o,j as E,f as j,E as B,G as C,H as F}from"./vue.CnN__PXn.js";let c,P=Promise.all([(()=>{try{return y}catch{}})()]).then(async()=>{let s,n;s={class:"dialog-footer"},n=i({name:"SysFileDialog"}),c=i({...n,emits:["refresh"],setup(T,{expose:f,emit:p}){const g=E(()=>x(()=>import("./index.uFcdAvQi.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3]))),v=p,e=m(!1),r=m([]),h=()=>{v("refresh")};return f({openDialog:()=>{r.value=[],e.value=!0}}),(a,l)=>{const V=_("el-button"),b=_("el-dialog");return D(),k(b,{title:a.$t("file.uploadFile"),modelValue:o(e),"onUpdate:modelValue":l[1]||(l[1]=u=>F(e)?e.value=u:null),"close-on-click-modal":!1,draggable:""},{footer:t(()=>[j("span",s,[d(V,{onClick:l[0]||(l[0]=u=>e.value=!1)},{default:t(()=>[B(C(a.$t("common.cancelButtonText")),1)]),_:1})])]),default:t(()=>[d(o(g),{onChange:h,"model-value":o(r)},null,8,["model-value"])]),_:1},8,["title","modelValue"])}}})});export{P as __tla,c as default};
