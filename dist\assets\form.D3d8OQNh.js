import{v as C,r as p,c as V,__tla as F}from"./index.BSP3cg_z.js";import{v as R,g as A,p as E,a as G,__tla as H}from"./dict.D9OX-VAS.js";import{d as _,k as b,A as y,B as c,m as P,a as z,b as T,t as r,v as o,q as J,e as K,u as t,f as M,E as I,G as $,H as N,y as Q}from"./vue.CnN__PXn.js";let k,W=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let g,f,h;g={class:"system-dic-dialog-container"},f={class:"dialog-footer"},h=_({name:"dict-item-form"}),k=_({...h,emits:["refresh"],setup(X,{expose:x,emit:O}){const U=O,{t:w}=C.useI18n(),n=b(),u=b(!1),m=b(!1),l=y({id:"",dictId:"",dictType:"",value:"",label:"",description:"",sortOrder:0,remarks:""}),q=y({value:[{validator:p.overLength,trigger:"blur"},{required:!0,message:"\u6570\u636E\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],label:[{validator:p.overLength,trigger:"blur"},{required:!0,message:"\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:(e,a,i)=>{R(e,a,i,l.dictType,l.id!=="")},trigger:"blur"}],description:[{validator:p.overLength,trigger:"blur"},{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sortOrder:[{validator:p.overLength,trigger:"blur"},{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),B=async()=>{if(!await n.value.validate().catch(()=>{}))return!1;try{m.value=!0,l.id?await E(l):await G(l),V().success(w(l.id?"common.editSuccessText":"common.addSuccessText")),u.value=!1,U("refresh")}catch(e){V().error(e.msg)}finally{m.value=!1}};return x({openDialog:(e,a)=>{u.value=!0,l.id="",Q(()=>{var i;(i=n.value)==null||i.resetFields(),a&&(l.dictId=a.dictId,l.dictType=a.dictType)}),e!=null&&e.id&&A(e.id).then(i=>{Object.assign(l,i.data)})}}),(e,a)=>{const i=c("el-input"),s=c("el-form-item"),L=c("el-input-number"),D=c("el-form"),v=c("el-button"),S=c("el-dialog"),j=P("loading");return T(),z("div",g,[r(S,{modelValue:t(u),"onUpdate:modelValue":a[7]||(a[7]=d=>N(u)?u.value=d:null),title:t(l).id?e.$t("common.editBtn"):e.$t("common.addBtn"),width:"600"},{footer:o(()=>[M("span",f,[r(v,{onClick:a[6]||(a[6]=d=>u.value=!1)},{default:o(()=>[I($(e.$t("common.cancelButtonText")),1)]),_:1}),r(v,{type:"primary",onClick:B,disabled:t(m)},{default:o(()=>[I($(e.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:o(()=>[J((T(),K(D,{ref_key:"dicDialogFormRef",ref:n,model:t(l),"label-width":"90px",rules:t(q)},{default:o(()=>[r(s,{label:e.$t("dictItem.dictType"),prop:"dictType"},{default:o(()=>[r(i,{modelValue:t(l).dictType,"onUpdate:modelValue":a[0]||(a[0]=d=>t(l).dictType=d),clearable:"",disabled:"",placeholder:e.$t("dictItem.inputDictTypeTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(s,{label:e.$t("dictItem.label"),prop:"label"},{default:o(()=>[r(i,{modelValue:t(l).label,"onUpdate:modelValue":a[1]||(a[1]=d=>t(l).label=d),placeholder:e.$t("dictItem.inputLabelTip"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(s,{label:e.$t("dictItem.itemValue"),prop:"value"},{default:o(()=>[r(i,{modelValue:t(l).value,"onUpdate:modelValue":a[2]||(a[2]=d=>t(l).value=d),placeholder:e.$t("dictItem.inputItemValueTip"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(s,{label:e.$t("dictItem.description"),prop:"description"},{default:o(()=>[r(i,{modelValue:t(l).description,"onUpdate:modelValue":a[3]||(a[3]=d=>t(l).description=d),placeholder:e.$t("dictItem.inputDescriptionTip"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(s,{label:e.$t("dictItem.sortOrder"),prop:"sortOrder"},{default:o(()=>[r(L,{modelValue:t(l).sortOrder,"onUpdate:modelValue":a[4]||(a[4]=d=>t(l).sortOrder=d),placeholder:e.$t("dictItem.inputSortOrderTip"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(s,{label:e.$t("dictItem.remarks"),prop:"remarks"},{default:o(()=>[r(i,{modelValue:t(l).remarks,"onUpdate:modelValue":a[5]||(a[5]=d=>t(l).remarks=d),type:"textarea",rows:"3",placeholder:e.$t("dictItem.inputRemarksTip"),maxlength:"150"},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[j,t(m)]])]),_:1},8,["modelValue","title"])])}}})});export{W as __tla,k as default};
