import{d as x,s as b,A as M,c as V,o as E,i as S,w as W,B as $,e as j,b as n,v as A,q as O,a,n as w,f as d,F as h,p as R,D as q,t as D,G as F,x as T,C as z}from"./vue.CnN__PXn.js";import{w as B,q as G,__tla as P}from"./index.BSP3cg_z.js";let C,H=Promise.all([(()=>{try{return P}catch{}})()]).then(async()=>{let r,u,p;r={class:"el-dropdown-menu"},u=["onClick"],p=x({name:"layoutTagsViewContextmenu"}),C=G(x({...p,props:{dropdown:{type:Object,default:()=>({x:0,y:0})}},emits:["currentContextmenuClick"],setup(_,{expose:k,emit:v}){const y=B(),{favoriteRoutes:L}=b(y),s=_,g=v,e=M({isShow:!1,dropdownList:[{contextMenuClickId:0,txt:"tagsView.refresh",affix:!1,show:!0,icon:"ele-RefreshRight"},{contextMenuClickId:1,txt:"tagsView.close",affix:!1,show:!0,icon:"ele-Close"},{contextMenuClickId:2,txt:"tagsView.closeOther",affix:!1,show:!0,icon:"ele-CircleClose"},{contextMenuClickId:3,txt:"tagsView.closeAll",affix:!1,show:!0,icon:"ele-FolderDelete"},{contextMenuClickId:4,txt:"tagsView.fullscreen",affix:!1,show:!0,icon:"iconfont icon-fullscreen"},{contextMenuClickId:5,txt:"tagsView.favorite",affix:!1,show:!0,icon:"ele-Star"}],item:{},arrowLeft:10}),m=V(()=>s.dropdown.x+117>document.documentElement.clientWidth?{x:document.documentElement.clientWidth-117-5,y:s.dropdown.y}:s.dropdown),i=()=>{e.isShow=!1};return E(()=>{document.body.addEventListener("click",i)}),S(()=>{document.body.removeEventListener("click",i)}),W(()=>s.dropdown,({x:t})=>{t+117>document.documentElement.clientWidth?e.arrowLeft=117-(document.documentElement.clientWidth-t):e.arrowLeft=10},{deep:!0}),k({openContextmenu:t=>{var l;e.item=t,(l=t.meta)!=null&&l.isAffix?e.dropdownList[1].affix=!0:e.dropdownList[1].affix=!1,L.value.find(c=>c.path===t.path)?e.dropdownList[5].show=!1:e.dropdownList[5].show=!0,i(),setTimeout(()=>{e.isShow=!0},10)}}),(t,l)=>{const c=$("SvgIcon");return n(),j(z,{name:"el-zoom-in-center"},{default:A(()=>[O((n(),a("div",{"aria-hidden":"true",class:"el-dropdown__popper el-popper is-light is-pure custom-contextmenu",role:"tooltip","data-popper-placement":"bottom",style:w(`top: ${m.value.y+5}px;left: ${m.value.x}px;`),key:Math.random()},[d("ul",r,[(n(!0),a(h,null,R(e.dropdownList,(o,I)=>(n(),a(h,null,[!o.affix&&o.show?(n(),a("li",{class:"el-dropdown-menu__item","aria-disabled":"false",tabindex:"-1",key:I,onClick:J=>{return f=o.contextMenuClickId,void g("currentContextmenuClick",Object.assign({},{contextMenuClickId:f},e.item));var f}},[D(c,{name:o.icon},null,8,["name"]),d("span",null,F(t.$t(o.txt)),1)],8,u)):q("",!0)],64))),256))]),d("div",{class:"el-popper__arrow",style:w({left:`${e.arrowLeft}px`})},null,4)],4)),[[T,e.isShow]])]),_:1})}}}),[["__scopeId","data-v-97fe536b"]])});export{H as __tla,C as default};
