import{ag as B,v as U,A as L,E as P,__tla as X}from"./index.BSP3cg_z.js";let R,$=Promise.all([(()=>{try{return X}catch{}})()]).then(async()=>{var j,Y={exports:{}};j=function(){return function(){var T={686:function(r,t,e){e.d(t,{default:function(){return q}});var a=e(279),s=e.n(a),i=e(370),f=e.n(i),h=e(817),y=e.n(h);function g(u){try{return document.execCommand(u)}catch{return!1}}var m=function(u){var o=y()(u);return g("cut"),o},w=function(u,o){var c=function(v){var b=document.documentElement.getAttribute("dir")==="rtl",n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[b?"right":"left"]="-9999px";var l=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(l,"px"),n.setAttribute("readonly",""),n.value=v,n}(u);o.container.appendChild(c);var p=y()(c);return g("copy"),c.remove(),p},C=function(u){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},c="";return typeof u=="string"?c=w(u,o):u instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(u==null?void 0:u.type)?c=w(u.value,o):(c=y()(u),g("copy")),c};function _(u){return _=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},_(u)}var N=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=u.action,c=o===void 0?"copy":o,p=u.container,v=u.target,b=u.text;if(c!=="copy"&&c!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(v!==void 0){if(!v||_(v)!=="object"||v.nodeType!==1)throw new Error('Invalid "target" value, use a valid Element');if(c==="copy"&&v.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(c==="cut"&&(v.hasAttribute("readonly")||v.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}return b?C(b,{container:p}):v?c==="cut"?m(v):C(v,{container:p}):void 0};function A(u){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},A(u)}function F(u,o){for(var c=0;c<o.length;c++){var p=o[c];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(u,p.key,p)}}function k(u,o){return k=Object.setPrototypeOf||function(c,p){return c.__proto__=p,c},k(u,o)}function D(u){var o=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}();return function(){var c,p,v,b=M(u);if(o){var n=M(this).constructor;c=Reflect.construct(b,arguments,n)}else c=b.apply(this,arguments);return p=this,!(v=c)||A(v)!=="object"&&typeof v!="function"?function(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}(p):v}}function M(u){return M=Object.setPrototypeOf?Object.getPrototypeOf:function(o){return o.__proto__||Object.getPrototypeOf(o)},M(u)}function O(u,o){var c="data-clipboard-".concat(u);if(o.hasAttribute(c))return o.getAttribute(c)}var I=function(u){(function(n,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(l&&l.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),l&&k(n,l)})(b,u);var o,c,p,v=D(b);function b(n,l){var S;return function(x,z){if(!(x instanceof z))throw new TypeError("Cannot call a class as a function")}(this,b),(S=v.call(this)).resolveOptions(l),S.listenClick(n),S}return o=b,c=[{key:"resolveOptions",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof n.action=="function"?n.action:this.defaultAction,this.target=typeof n.target=="function"?n.target:this.defaultTarget,this.text=typeof n.text=="function"?n.text:this.defaultText,this.container=A(n.container)==="object"?n.container:document.body}},{key:"listenClick",value:function(n){var l=this;this.listener=f()(n,"click",function(S){return l.onClick(S)})}},{key:"onClick",value:function(n){var l=n.delegateTarget||n.currentTarget,S=this.action(l)||"copy",x=N({action:S,container:this.container,target:this.target(l),text:this.text(l)});this.emit(x?"success":"error",{action:S,text:x,trigger:l,clearSelection:function(){l&&l.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(n){return O("action",n)}},{key:"defaultTarget",value:function(n){var l=O("target",n);if(l)return document.querySelector(l)}},{key:"defaultText",value:function(n){return O("text",n)}},{key:"destroy",value:function(){this.listener.destroy()}}],p=[{key:"copy",value:function(n){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return C(n,l)}},{key:"cut",value:function(n){return m(n)}},{key:"isSupported",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],l=typeof n=="string"?[n]:n,S=!!document.queryCommandSupported;return l.forEach(function(x){S=S&&!!document.queryCommandSupported(x)}),S}}],c&&F(o.prototype,c),p&&F(o,p),b}(s()),q=I},828:function(r){if(typeof Element<"u"&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}r.exports=function(e,a){for(;e&&e.nodeType!==9;){if(typeof e.matches=="function"&&e.matches(a))return e;e=e.parentNode}}},438:function(r,t,e){var a=e(828);function s(f,h,y,g,m){var w=i.apply(this,arguments);return f.addEventListener(y,w,m),{destroy:function(){f.removeEventListener(y,w,m)}}}function i(f,h,y,g){return function(m){m.delegateTarget=a(m.target,h),m.delegateTarget&&g.call(f,m)}}r.exports=function(f,h,y,g,m){return typeof f.addEventListener=="function"?s.apply(null,arguments):typeof y=="function"?s.bind(null,document).apply(null,arguments):(typeof f=="string"&&(f=document.querySelectorAll(f)),Array.prototype.map.call(f,function(w){return s(w,h,y,g,m)}))}},879:function(r,t){t.node=function(e){return e!==void 0&&e instanceof HTMLElement&&e.nodeType===1},t.nodeList=function(e){var a=Object.prototype.toString.call(e);return e!==void 0&&(a==="[object NodeList]"||a==="[object HTMLCollection]")&&"length"in e&&(e.length===0||t.node(e[0]))},t.string=function(e){return typeof e=="string"||e instanceof String},t.fn=function(e){return Object.prototype.toString.call(e)==="[object Function]"}},370:function(r,t,e){var a=e(879),s=e(438);r.exports=function(i,f,h){if(!i&&!f&&!h)throw new Error("Missing required arguments");if(!a.string(f))throw new TypeError("Second argument must be a String");if(!a.fn(h))throw new TypeError("Third argument must be a Function");if(a.node(i))return function(y,g,m){return y.addEventListener(g,m),{destroy:function(){y.removeEventListener(g,m)}}}(i,f,h);if(a.nodeList(i))return function(y,g,m){return Array.prototype.forEach.call(y,function(w){w.addEventListener(g,m)}),{destroy:function(){Array.prototype.forEach.call(y,function(w){w.removeEventListener(g,m)})}}}(i,f,h);if(a.string(i))return function(y,g,m){return s(document.body,y,g,m)}(i,f,h);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(r){r.exports=function(t){var e;if(t.nodeName==="SELECT")t.focus(),e=t.value;else if(t.nodeName==="INPUT"||t.nodeName==="TEXTAREA"){var a=t.hasAttribute("readonly");a||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),a||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var s=window.getSelection(),i=document.createRange();i.selectNodeContents(t),s.removeAllRanges(),s.addRange(i),e=s.toString()}return e}},279:function(r){function t(){}t.prototype={on:function(e,a,s){var i=this.e||(this.e={});return(i[e]||(i[e]=[])).push({fn:a,ctx:s}),this},once:function(e,a,s){var i=this;function f(){i.off(e,f),a.apply(s,arguments)}return f._=a,this.on(e,f,s)},emit:function(e){for(var a=[].slice.call(arguments,1),s=((this.e||(this.e={}))[e]||[]).slice(),i=0,f=s.length;i<f;i++)s[i].fn.apply(s[i].ctx,a);return this},off:function(e,a){var s=this.e||(this.e={}),i=s[e],f=[];if(i&&a)for(var h=0,y=i.length;h<y;h++)i[h].fn!==a&&i[h].fn._!==a&&f.push(i[h]);return f.length?s[e]=f:delete s[e],this}},r.exports=t,r.exports.TinyEmitter=t}},E={};function d(r){if(E[r])return E[r].exports;var t=E[r]={exports:{}};return T[r](t,t.exports,d),t.exports}return d.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return d.d(t,{a:t}),t},d.d=function(r,t){for(var e in t)d.o(t,e)&&!d.o(r,e)&&Object.defineProperty(r,e,{enumerable:!0,get:t[e]})},d.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},d(686)}().default};const H=B(Y.exports=j());R=function(){const{t:T}=U.useI18n(),{toClipboard:E}={toClipboard:(d,r)=>new Promise((t,e)=>{const a=document.createElement("button"),s=new H(a,{text:()=>d,action:()=>"copy",container:r!==void 0?r:document.body});s.on("success",i=>{s.destroy(),t(i)}),s.on("error",i=>{s.destroy(),e(i)}),document.body.appendChild(a),a.click(),document.body.removeChild(a)})};return{percentFormat:(d,r,t)=>t?`${t}%`:"-",dateFormatYMD:(d,r,t)=>t?L(new Date(t),"YYYY-mm-dd"):"-",dateFormatYMDHMS:(d,r,t)=>t?L(new Date(t),"YYYY-mm-dd HH:MM:SS"):"-",dateFormatHMS:(d,r,t)=>{if(!t)return"-";let e=0;return typeof d=="number"&&(e=d),typeof t=="number"&&(e=t),L(new Date(1e3*e),"HH:MM:SS")},scaleFormat:(d="0",r=4)=>Number.parseFloat(d).toFixed(r),scale2Format:(d="0")=>Number.parseFloat(d).toFixed(2),copyText:d=>new Promise((r,t)=>{try{E(d),P.success(T("layout.copyTextSuccess")),r(d)}catch(e){P.error(T("layout.copyTextError")),t(e)}}),getRandomColor:()=>{let d="#";for(let r=0;r<6;r++)d+="0123456789ABCDEF"[Math.floor(16*Math.random())];return d}}}});export{$ as __tla,R as c};
