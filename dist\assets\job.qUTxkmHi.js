import{s as o,__tla as l}from"./index.BSP3cg_z.js";let s,r,n,e,u,a,b,j,d=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{e=function(t){return o({url:"/job/sys-job/page",method:"get",params:t})},s=function(t){return o({url:"/job/sys-job",method:"post",data:t})},u=function(t){return o({url:"/job/sys-job/"+t,method:"get"})},n=function(t){return o({url:"/job/sys-job/"+t,method:"delete"})},a=function(t){return o({url:"/job/sys-job",method:"put",data:t})},j=function(t){return o({url:"/job/sys-job/start-job/"+t,method:"post"})},b=function(t){return o({url:"/job/sys-job/run-job/"+t,method:"post"})},r=function(t){return o({url:"/job/sys-job/shutdown-job/"+t,method:"post"})}});export{d as __tla,s as a,r as b,n as d,e as f,u as g,a as p,b as r,j as s};
