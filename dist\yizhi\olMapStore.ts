/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-05-16 09:27:53
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-06-27 16:36:11
 * @FilePath: \uavflight-ui\public\yizhi\olMapStore.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/**
 * OpenLayers 地图状态管理模块
 */
import { defineStore } from 'pinia';
import OLMap from '/@/utils/map/olMap';
import type { useMapLayerManagerStore } from './mapLayer/mapLayerManager';
import { transform } from 'ol/proj';

export const useOLMapStore = defineStore({
	// 模块ID
	id: 'olMapStore',
	state: () => {
		return {
			map: null as OLMap | null,
			// 图层管理器引用
			mapLayerManager: null as ReturnType<typeof useMapLayerManagerStore> | null,
			// 相机监控定时器ID
			cameraMonitorId: null as ReturnType<typeof setInterval> | null,
		};
	},
	actions: {
		/**
		 * 输出相机位置和分辨率信息到控制台
		 */
		logCameraInfo() {
			if (!this.map || !this.map.map) return;
			
			try {
				const view = this.map.map.getView();
				const center = view.getCenter();
				const zoom = view.getZoom();
				const resolution = view.getResolution();
				const rotation = view.getRotation();
				const extent = view.calculateExtent(this.map.map.getSize());
				
				// 坐标投影转换为经纬度 (EPSG:3857 -> EPSG:4326)
				let lonLat = center ? transform(center, view.getProjection().getCode(), 'EPSG:4326') : [0, 0];
				
				console.log('===== 相机信息 =====');
				console.log(`中心点坐标: [${center?.[0].toFixed(6)}, ${center?.[1].toFixed(6)}]`);
				console.log(`经纬度坐标: [${lonLat[0].toFixed(6)}, ${lonLat[1].toFixed(6)}]`);
				console.log(`缩放级别: ${zoom?.toFixed(2)}`);
				console.log(`分辨率: ${resolution?.toFixed(6)}`);
				console.log(`旋转角度: ${rotation}`);
				console.log(`视图范围: [${extent[0].toFixed(2)}, ${extent[1].toFixed(2)}, ${extent[2].toFixed(2)}, ${extent[3].toFixed(2)}]`);
				console.log('===================');
			} catch (error) {
				console.error('获取相机信息失败:', error);
			}
		},
		
		/**
		 * 初始化相机信息监控
		 */
		initCameraMonitor() {
			// 先清除已有的监控
			this.clearCameraMonitor();
			
			// 设置新的监控，每2秒输出一次相机信息
			this.cameraMonitorId = setInterval(() => {
				this.logCameraInfo();
			}, 2000);
			
			// 立即输出一次当前信息
			this.logCameraInfo();
			
			console.log('已启动相机信息监控 (每2秒)');
		},
		
		/**
		 * 清除相机监控
		 */
		clearCameraMonitor() {
			if (this.cameraMonitorId !== null) {
				clearInterval(this.cameraMonitorId);
				this.cameraMonitorId = null;
				console.log('已停止相机信息监控');
			}
		},
		
		/**
		 * 初始化地图及相关组件
		 */
		init() {
			// 如果没有初始化地图，则不执行后续代码
			if (!this.map) return;
			
			try {
				// 移除地图默认控件（比例尺、缩放按钮、全屏按钮等）
				if (this.map.map) {
					const controls = this.map.map.getControls();
					// 获取所有控件并移除
					controls.getArray().slice(0).forEach(control => {
						this.map?.map?.removeControl(control);
					});
					console.log('已移除地图默认控件');
				}
				
				// 从配置文件加载地图初始位置
				this.loadInitialViewFromConfig()
					.then(() => {
						console.log('已从配置文件加载地图初始位置');
					})
					.catch(error => {
						console.error('加载地图初始位置失败:', error);
						// 如果加载失败，使用默认位置
						this.map?.flyTo(110.877367, 23.787370, 10, 1000);
					});
				
				// 初始化相机监控
				this.initCameraMonitor();
				
				// 初始化地图图层管理
				// 延迟导入 mapLayerManager，避免循环依赖
				setTimeout(() => {
					import('./mapLayer/mapLayerManager').then(({ useMapLayerManagerStore }) => {
						this.mapLayerManager = useMapLayerManagerStore();
						
						// 使用新的初始化方法，一步完成加载配置和初始化图层
						if (this.map && this.map.map) {
							this.mapLayerManager.loadMapConfigAndInitialize(this.map.map)
								.then(success => {
									if (success) {
										console.log('地图配置加载成功并初始化了默认图层');
										
										// 可以显示加载了哪些图层的信息
										console.log('已加载的图层:', this.mapLayerManager?.loadedLayers);
										
										// 这里可以添加其他初始化后的操作
									} else {
										console.warn('地图配置加载失败或初始化失败');
									}
								})
								.catch(error => {
									console.error('加载地图配置并初始化图层失败:', error);
								});
						} else {
							console.warn('OpenLayers 地图未初始化，无法加载地图图层');
						}
					}).catch(err => {
						console.error('导入 mapLayerManager 失败:', err);
					});
				}, 100);
			} catch (error) {
				console.error('初始化地图出错:', error);
			}
		},
		
		/**
		 * 从配置文件加载地图初始视图
		 * 读取baseMap2.json中的initialView配置
		 */
		async loadInitialViewFromConfig() {
			try {
				// 从环境变量获取地图服务IP地址和URL
				const mapServerIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
				const mapServerUrl = `http://${mapServerIp}:${import.meta.env.VITE_MAP_SERVER_PORT_NGINX}`;
				
				// 构建配置文件URL
				const configUrl = `${mapServerUrl}/baseMap2.json`;
				console.log('正在加载地图初始视图配置，URL:', configUrl);
				
				const response = await fetch(configUrl);
				if (!response.ok) {
					throw new Error(`加载地图配置失败: ${response.status}`);
				}
				
				const config = await response.json();
				if (config.mapConfig && config.mapConfig.initialView) {
					const { center, zoom, duration } = config.mapConfig.initialView;
					
					if (Array.isArray(center) && center.length === 2) {
						// 使用配置文件中的初始位置
						this.map?.flyTo(center[0], center[1], zoom || 10, duration || 1000);
						console.log(`地图初始位置已设置为 [${center[0]}, ${center[1]}], 缩放级别: ${zoom}`);
						return true;
					}
				}
				throw new Error('配置文件中缺少有效的initialView配置');
			} catch (error) {
				console.error('加载地图初始视图配置失败:', error);
				throw error;
			}
		},
		
		/**
		 * 设置地图实例并初始化
		 * @param target 地图容器元素ID或DOM元素
		 */
		initMap(target: string | HTMLElement) {
			this.map = new OLMap(target);
			this.init();
		},
		
		/**
		 * 获取地图实例
		 * @returns OLMap实例或undefined
		 */
		getMap() {
			return this.map;
		}
	}
}); 