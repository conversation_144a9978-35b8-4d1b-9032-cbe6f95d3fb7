define(["./defaultValue-0a909f67","./Matrix3-a348023f","./arrayRemoveDuplicates-e9673044","./BoundingRectangle-e6c89457","./Transforms-01e95659","./Matrix2-7146c9ca","./ComponentDatatype-77274976","./PolylineVolumeGeometryLibrary-d240c597","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./IndexDatatype-2149f06c","./Math-e97915da","./PolygonPipeline-f5f5011c","./combine-ca22a614","./RuntimeError-06c93819","./WebGLConstants-a8cc3e8c","./EllipsoidTangentPlane-6308603a","./AxisAlignedBoundingBox-65ccb1a5","./IntersectionTests-0bb04fde","./Plane-8575e17c","./PolylinePipeline-e5af032d","./EllipsoidGeodesic-f4dd0b26","./EllipsoidRhumbLine-9b24aab2"],(function(e,t,i,n,o,a,l,r,s,p,c,d,u,y,g,h,f,m,E,P,_,k,b){"use strict";function C(i){const n=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).polylinePositions,o=i.shapePositions;this._positions=n,this._shape=o,this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(i.ellipsoid,t.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(i.cornerType,r.CornerType.ROUNDED),this._granularity=e.defaultValue(i.granularity,d.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeOutlineGeometry";let l=1+n.length*t.Cartesian3.packedLength;l+=1+o.length*a.Cartesian2.packedLength,this.packedLength=l+t.Ellipsoid.packedLength+2}C.pack=function(i,n,o){let l;o=e.defaultValue(o,0);const r=i._positions;let s=r.length;for(n[o++]=s,l=0;l<s;++l,o+=t.Cartesian3.packedLength)t.Cartesian3.pack(r[l],n,o);const p=i._shape;for(s=p.length,n[o++]=s,l=0;l<s;++l,o+=a.Cartesian2.packedLength)a.Cartesian2.pack(p[l],n,o);return t.Ellipsoid.pack(i._ellipsoid,n,o),o+=t.Ellipsoid.packedLength,n[o++]=i._cornerType,n[o]=i._granularity,n};const L=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),T={polylinePositions:void 0,shapePositions:void 0,ellipsoid:L,height:void 0,cornerType:void 0,granularity:void 0};C.unpack=function(i,n,o){let l;n=e.defaultValue(n,0);let r=i[n++];const s=new Array(r);for(l=0;l<r;++l,n+=t.Cartesian3.packedLength)s[l]=t.Cartesian3.unpack(i,n);r=i[n++];const p=new Array(r);for(l=0;l<r;++l,n+=a.Cartesian2.packedLength)p[l]=a.Cartesian2.unpack(i,n);const c=t.Ellipsoid.unpack(i,n,L);n+=t.Ellipsoid.packedLength;const d=i[n++],u=i[n];return e.defined(o)?(o._positions=s,o._shape=p,o._ellipsoid=t.Ellipsoid.clone(c,o._ellipsoid),o._cornerType=d,o._granularity=u,o):(T.polylinePositions=s,T.shapePositions=p,T.cornerType=d,T.granularity=u,new C(T))};const D=new n.BoundingRectangle;return C.createGeometry=function(e){const a=e._positions,d=i.arrayRemoveDuplicates(a,t.Cartesian3.equalsEpsilon);let y=e._shape;if(y=r.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(y),d.length<2||y.length<3)return;u.PolygonPipeline.computeWindingOrder2D(y)===u.WindingOrder.CLOCKWISE&&y.reverse();const g=n.BoundingRectangle.fromPoints(y,D);return function(e,t){const i=new p.GeometryAttributes;i.position=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e});const n=t.length,a=i.position.values.length/3,r=e.length/3/n,d=c.IndexDatatype.createTypedArray(a,2*n*(r+1));let u,y,g=0;u=0;let h=u*n;for(y=0;y<n-1;y++)d[g++]=y+h,d[g++]=y+h+1;for(d[g++]=n-1+h,d[g++]=h,u=r-1,h=u*n,y=0;y<n-1;y++)d[g++]=y+h,d[g++]=y+h+1;for(d[g++]=n-1+h,d[g++]=h,u=0;u<r-1;u++){const e=n*u,t=e+n;for(y=0;y<n;y++)d[g++]=y+e,d[g++]=y+t}return new s.Geometry({attributes:i,indices:c.IndexDatatype.createTypedArray(a,d),boundingSphere:o.BoundingSphere.fromVertices(e),primitiveType:s.PrimitiveType.LINES})}(r.PolylineVolumeGeometryLibrary.computePositions(d,y,g,e,!1),y)},function(i,n){return e.defined(n)&&(i=C.unpack(i,n)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),C.createGeometry(i)}}));
