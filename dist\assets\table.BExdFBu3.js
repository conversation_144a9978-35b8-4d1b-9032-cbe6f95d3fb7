import{s as t,__tla as _}from"./index.BSP3cg_z.js";let r,n,o,g,l,m,s,u,d,f,i,p,c,h,v=Promise.all([(()=>{try{return _}catch{}})()]).then(async()=>{r=function(e){return t({url:"/gen/table/page",method:"get",params:e})},c=function(e){return t({url:"/gen/table",method:"put",data:e})},n=(e,a)=>t.get("/gen/table/sync/"+e+"/"+a),h=(e,a)=>t.get("/gen/table/"+e+"/"+a),i=(e,a,b)=>t.put("/gen/table/field/"+e+"/"+a,b),d=e=>t({url:"/gen/generator/code",method:"get",params:{tableIds:e}}),o=(e,a)=>t({url:"/gen/generator/vform",method:"get",params:{dsName:e,tableName:a}}),u=e=>t({url:"/gen/generator/vform/sfc",method:"get",params:{formId:e}}),p=e=>t({url:"/gen/generator/preview",method:"get",params:{tableId:e}}),f=function(){return t({url:"/admin/dict/list",method:"get"})},s=function(e){return t({url:"/gen/form",method:"post",data:e})},m=function(e){return t({url:"/gen/form/page",method:"get",params:e})},l=function(e){return t({url:"/gen/form/"+e,method:"get"})},g=function(e){return t({url:"/gen/form/"+e,method:"delete"})}});export{v as __tla,r as a,n as b,o as c,g as d,l as e,m as f,s as g,u as h,d as i,f as j,i as k,p as l,c as p,h as u};
