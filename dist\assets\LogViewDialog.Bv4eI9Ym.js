import{d as B,c as D,k as h,w as P,B as u,e as S,b as r,v as I,f as n,t as V,E as q,a as c,G as z}from"./vue.CnN__PXn.js";import{q as C,__tla as G}from"./index.BSP3cg_z.js";let $,H=Promise.all([(()=>{try{return G}catch{}})()]).then(async()=>{let m,p,v,g,y,k,_;m={class:"log-dialog"},p={class:"content-box"},v={class:"content-header"},g={class:"content-body"},y={key:0,class:"loading-content"},k={key:1,class:"empty-content"},_={key:2,class:"log-content"},$=C(B({__name:"LogViewDialog",props:{modelValue:{type:Boolean},taskId:{}},emits:["update:modelValue"],setup(b,{emit:E}){const s=b,j=E,f=D({get:()=>s.modelValue,set:e=>j("update:modelValue",e)}),d=h(!1),l=h(""),w=async e=>{try{d.value=!0;const a=`http://192.168.43.148:8091/api/analysis/logs/?task_id=${e}`,o=await fetch(a);if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);const t=await o.json();if(t.status!=="success"||!t.data)throw new Error(t.message||"\u83B7\u53D6\u65E5\u5FD7\u5931\u8D25");t.data.logs&&Array.isArray(t.data.logs)?l.value=t.data.logs.map(i=>i.replace(/\n$/,"")).join(`
`):l.value="\u6682\u65E0\u65E5\u5FD7\u4FE1\u606F"}catch{l.value=x(e)}finally{d.value=!1}},x=e=>{const a=new Date().toISOString().replace("T"," ").substring(0,19);return`[${a}] \u5DF2\u521B\u5EFA\u5206\u6790\u4EFB\u52A1: ${e}
[${a}] \u6B63\u5728\u6392\u961F\u4E2D\uFF0C\u7B49\u5F85\u6267\u884C\u3002`},T=()=>{s.taskId&&w(s.taskId)};return P(()=>s.modelValue,e=>{e&&s.taskId&&w(s.taskId)}),(e,a)=>{const o=u("el-button"),t=u("el-skeleton"),i=u("el-dialog");return r(),S(i,{modelValue:f.value,"onUpdate:modelValue":a[0]||(a[0]=A=>f.value=A),title:"\u4EFB\u52A1\u65E5\u5FD7: "+e.taskId,width:"80%","destroy-on-close":""},{default:I(()=>[n("div",m,[n("div",p,[n("div",v,[a[2]||(a[2]=n("span",null,"\u4EFB\u52A1\u65E5\u5FD7:",-1)),V(o,{type:"primary",size:"small",loading:d.value,onClick:T},{default:I(()=>a[1]||(a[1]=[q(" \u5237\u65B0 ")])),_:1},8,["loading"])]),n("div",g,[d.value?(r(),c("div",y,[V(t,{rows:10,animated:""})])):l.value?(r(),c("pre",_,z(l.value),1)):(r(),c("div",k," \u6682\u65E0\u4EFB\u52A1\u65E5\u5FD7\u4FE1\u606F\uFF0C\u8BF7\u70B9\u51FB\u5237\u65B0\u6309\u94AE\u83B7\u53D6 "))])])])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-575e47cf"]])});export{H as __tla,$ as default};
