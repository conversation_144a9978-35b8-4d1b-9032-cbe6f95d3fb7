import{s as t,__tla as w}from"./index.BSP3cg_z.js";let d,o,m,i,l,p,h,c,f,g,_,E=Promise.all([(()=>{try{return w}catch{}})()]).then(async()=>{m=e=>t({url:"/admin/user/page",method:"get",params:e}),d=e=>t({url:"/admin/user",method:"post",data:e}),h=e=>t({url:"/admin/user/details/"+e,method:"get"}),i=e=>t({url:"/admin/user",method:"delete",data:e}),f=e=>t({url:"/admin/user",method:"put",data:e});function u(e){return t({url:"/admin/user/details",method:"get",params:e})}c=function(e){return t({url:"/admin/user/edit",method:"put",data:e})},p=function(e){return t({url:"/admin/user/password",method:"put",data:e})},l=function(e){return t({url:"/admin/user/check",method:"post",params:{password:e}})},g=e=>t({url:"/admin/register/user",method:"post",data:e}),o=function(e,a,r,n){if(new RegExp(/^([a-z\d]+?)$/).test(a)||r(new Error("\u7528\u6237\u540D\u652F\u6301\u5C0F\u5199\u82F1\u6587\u3001\u6570\u5B57")),n)return r();u({username:a}).then(s=>{s.data!==null?r(new Error("\u7528\u6237\u540D\u5DF2\u7ECF\u5B58\u5728")):r()})},_=function(e,a,r,n){if(n)return r();u({phone:a}).then(s=>{s.data!==null?r(new Error("\u624B\u673A\u53F7\u5DF2\u7ECF\u5B58\u5728")):r()})}});export{E as __tla,d as a,o as b,m as c,i as d,l as e,p as f,h as g,c as h,f as p,g as r,_ as v};
