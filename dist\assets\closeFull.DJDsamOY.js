import{d as t,s as i,B as m,a as _,D as d,u as f,b as v,f as y,t as p}from"./vue.CnN__PXn.js";import{w as C,q as F,__tla as b}from"./index.BSP3cg_z.js";let r,g=Promise.all([(()=>{try{return b}catch{}})()]).then(async()=>{let s,a,l;s={key:0,class:"layout-navbars-close-full"},a={class:"layout-navbars-close-full-icon"},l=t({name:"layoutCloseFull"}),r=F(t({...l,setup(w){const e=C(),{isTagsViewCurrenFull:o}=i(e),n=()=>{e.setCurrenFullscreen(!1)};return(u,h)=>{const c=m("SvgIcon");return f(o)?(v(),_("div",s,[y("div",a,[p(c,{name:"ele-Close",title:u.$t("tagsView.closeFullscreen"),onClick:n},null,8,["title"])])])):d("",!0)}}}),[["__scopeId","data-v-204d02f5"]])});export{g as __tla,r as default};
