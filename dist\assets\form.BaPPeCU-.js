import{u as V,__tla as I}from"./table.CCFM44Zd.js";import{f as j,d as A,__tla as B}from"./table.BExdFBu3.js";import{v as E,d as J,c as g,__tla as P}from"./index.BSP3cg_z.js";import{d as p,l as U,k as G,A as K,B as l,m as M,e as y,b as f,v as o,q as O,t as r,u as a,E as C,J as Q,H as R}from"./vue.CnN__PXn.js";let b,W=Promise.all([(()=>{try{return I}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{let m;m=p({name:"systemFormHistoryDialog"}),b=p({...m,emits:["refresh"],setup(X,{expose:w,emit:v}){const x=v,k=U(),{t:c}=E.useI18n(),n=G(!1),t=K({queryForm:{},pageList:j,createdIsNeed:!1,descs:["create_time"]}),{getDataList:u,currentChangeHandle:q,sizeChangeHandle:H,sortChangeHandle:N}=V(t);return w({openDialog:()=>{const{tableName:_,dsName:e}=k.query;n.value=!0,t.queryForm.dsName=e,t.queryForm.tableName=_,u()}}),(_,e)=>{const d=l("el-table-column"),h=l("el-button"),S=l("el-table"),F=l("pagination"),z=l("el-drawer"),D=M("loading");return f(),y(z,{title:"\u8BBE\u8BA1\u5386\u53F2",modelValue:a(n),"onUpdate:modelValue":e[0]||(e[0]=s=>R(n)?n.value=s:null),draggable:""},{default:o(()=>[O((f(),y(S,{data:a(t).dataList,style:{width:"100%"},onSortChange:a(N)},{default:o(()=>[r(d,{type:"index",label:"\u5E8F\u53F7",width:"60"}),r(d,{prop:"createTime",label:"\u8BBE\u8BA1\u65F6\u95F4","show-overflow-tooltip":""}),r(d,{label:"\u64CD\u4F5C",width:"150"},{default:o(s=>[r(h,{icon:"refresh",text:"",type:"primary",onClick:L=>{return i=s.row.id,void x("refresh",i);var i}},{default:o(()=>e[1]||(e[1]=[C("\u56DE\u6EDA")])),_:2},1032,["onClick"]),r(h,{icon:"delete",text:"",type:"primary",onClick:L=>(async i=>{try{await J().confirm(c("common.delConfirmText"))}catch{return}try{await A(i),u(),g().success(c("common.delSuccessText"))}catch(T){g().error(T.msg)}})(s.row.id)},{default:o(()=>e[2]||(e[2]=[C("\u5220\u9664")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","onSortChange"])),[[D,a(t).loading]]),r(F,Q({onSizeChange:a(H),onCurrentChange:a(q)},a(t).pagination),null,16,["onSizeChange","onCurrentChange"])]),_:1},8,["modelValue"])}}})});export{W as __tla,b as default};
