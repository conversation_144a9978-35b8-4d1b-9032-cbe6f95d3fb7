import{v as Q,c as k,r as f,__tla as W}from"./index.BSP3cg_z.js";import{p as X,a as Y,g as Z,__tla as ee}from"./datasource.DryyxFrZ.js";import{u as ae,__tla as le}from"./dict.DrX0Qdnc.js";import{d as x,k as h,A as re,B as u,m as oe,e as m,b as n,v as o,q as te,u as e,t,D as b,a as L,F as N,p as B,E as v,G as V,f as se,H as de,y as ue}from"./vue.CnN__PXn.js";import{__tla as ne}from"./dict.D9OX-VAS.js";let D,pe=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return ne}catch{}})()]).then(async()=>{let w,q;w={class:"dialog-footer"},q=x({name:"systemDatasourceConfDialog"}),D=x({...q,emits:["refresh"],setup(ce,{expose:$,emit:j}){const C=j,{t:s}=Q.useI18n(),T=h(),g=h(!1),_=h(!1),{ds_config_type:F,ds_type:R}=ae("ds_config_type","ds_type"),a=re({id:"",name:"",url:"",username:"",password:void 0,createTime:"",updateTime:"",dsType:"mysql",confType:0,dsName:"",instance:"",port:3306,host:""}),A=h({name:[{required:!0,message:"\u522B\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:f.overLength,trigger:"blur"},{validator:(d,l,y)=>{l&&!/(?=.*[a-z])(?=.*_)/.test(l)?y(new Error("\u6570\u636E\u6E90\u540D\u79F0\u4E0D\u5408\u6CD5, \u7EC4\u540D_\u6570\u636E\u6E90\u540D\u5F62\u5F0F")):y()},trigger:"blur"}],url:[{required:!0,message:"jdbcurl\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{min:10,max:500,message:"URL\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 10 \u548C 500 \u5B57\u7B26\u4E4B\u95F4",trigger:"blur"}],username:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],password:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],dsType:[{required:!0,message:"\u6570\u636E\u5E93\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],confType:[{required:!0,message:"\u914D\u7F6E\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],dsName:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u6570\u636E\u5E93\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],instance:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u5B9E\u4F8B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],port:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u7AEF\u53E3\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],host:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u4E3B\u673A\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),E=async()=>{var d;if(!await T.value.validate().catch(()=>{}))return!1;a.password=(d=a.password)!=null&&d.includes("******")?void 0:a.password;try{_.value=!0,a.id?await X(a):await Y(a),k().success(s(a.id?"common.editSuccessText":"common.addSuccessText")),g.value=!1,C("refresh")}catch(l){k().error(l.msg)}finally{_.value=!1}},S=d=>Z(d).then(l=>{Object.assign(a,l.data)});return $({openDialog:async d=>{g.value=!0,a.id="",ue(()=>{var l;(l=T.value)==null||l.resetFields()}),d&&(a.id=d,await S(d),a.password="********")}}),(d,l)=>{const y=u("el-option"),z=u("el-select"),p=u("el-form-item"),c=u("el-col"),i=u("el-input"),G=u("el-radio"),H=u("el-radio-group"),I=u("el-input-number"),O=u("el-row"),P=u("el-form"),U=u("el-button"),J=u("el-dialog"),K=oe("loading");return n(),m(J,{title:e(a).id?d.$t("common.editBtn"):d.$t("common.addBtn"),modelValue:e(g),"onUpdate:modelValue":l[11]||(l[11]=r=>de(g)?g.value=r:null),"close-on-click-modal":!1,draggable:""},{footer:o(()=>[se("span",w,[t(U,{onClick:l[10]||(l[10]=r=>g.value=!1)},{default:o(()=>[v(V(d.$t("common.cancelButtonText")),1)]),_:1}),t(U,{type:"primary",onClick:E,disabled:e(_)},{default:o(()=>[v(V(d.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:o(()=>[te((n(),m(P,{ref_key:"dataFormRef",ref:T,model:e(a),rules:e(A),formDialogRef:"","label-width":"90px"},{default:o(()=>[t(O,{gutter:20},{default:o(()=>[t(c,{span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.dsType"),prop:"dsType"},{default:o(()=>[t(z,{modelValue:e(a).dsType,"onUpdate:modelValue":l[0]||(l[0]=r=>e(a).dsType=r),placeholder:e(s)("datasourceconf.inputdsTypeTip")},{default:o(()=>[(n(!0),L(N,null,B(e(R),r=>(n(),m(y,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),t(c,{span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.name"),prop:"name"},{default:o(()=>[t(i,{modelValue:e(a).name,"onUpdate:modelValue":l[1]||(l[1]=r=>e(a).name=r),placeholder:e(s)("datasourceconf.inputnameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),t(c,{span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.username"),prop:"username"},{default:o(()=>[t(i,{modelValue:e(a).username,"onUpdate:modelValue":l[2]||(l[2]=r=>e(a).username=r),placeholder:e(s)("datasourceconf.inputusernameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),t(c,{span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.password"),prop:"password"},{default:o(()=>[t(i,{modelValue:e(a).password,"onUpdate:modelValue":l[3]||(l[3]=r=>e(a).password=r),placeholder:e(s)("datasourceconf.inputpasswordTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),t(c,{span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.confType"),prop:"confType"},{default:o(()=>[t(H,{modelValue:e(a).confType,"onUpdate:modelValue":l[4]||(l[4]=r=>e(a).confType=r)},{default:o(()=>[(n(!0),L(N,null,B(e(F),(r,M)=>(n(),m(G,{label:Number(r.value),border:"",key:M},{default:o(()=>[v(V(r.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1}),e(a).confType===0&&e(a).dsType==="mssql"?(n(),m(c,{key:0,span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.instance"),prop:"instance"},{default:o(()=>[t(i,{modelValue:e(a).instance,"onUpdate:modelValue":l[5]||(l[5]=r=>e(a).instance=r),placeholder:e(s)("datasourceconf.inputinstanceTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})):b("",!0),e(a).confType===0?(n(),m(c,{key:1,span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.port"),prop:"port"},{default:o(()=>[t(I,{modelValue:e(a).port,"onUpdate:modelValue":l[6]||(l[6]=r=>e(a).port=r),placeholder:e(s)("datasourceconf.inputportTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})):b("",!0),e(a).confType===0?(n(),m(c,{key:2,span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.host"),prop:"host"},{default:o(()=>[t(i,{modelValue:e(a).host,"onUpdate:modelValue":l[7]||(l[7]=r=>e(a).host=r),placeholder:e(s)("datasourceconf.inputhostTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})):b("",!0),e(a).confType===0?(n(),m(c,{key:3,span:12,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.dsName"),prop:"dsName"},{default:o(()=>[t(i,{modelValue:e(a).dsName,"onUpdate:modelValue":l[8]||(l[8]=r=>e(a).dsName=r),placeholder:e(s)("datasourceconf.inputdsNameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})):b("",!0),e(a).confType===1?(n(),m(c,{key:4,span:24,class:"mb20"},{default:o(()=>[t(p,{label:e(s)("datasourceconf.url"),prop:"url"},{default:o(()=>[t(i,{modelValue:e(a).url,"onUpdate:modelValue":l[9]||(l[9]=r=>e(a).url=r),type:"textarea",placeholder:e(s)("datasourceconf.inputurlTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})):b("",!0)]),_:1})]),_:1},8,["model","rules"])),[[K,e(_)]])]),_:1},8,["title","modelValue"])}}})});export{pe as __tla,D as default};
