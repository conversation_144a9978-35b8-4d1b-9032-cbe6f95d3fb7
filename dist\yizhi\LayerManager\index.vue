<!--
 * @file index.vue
 * @description 图层管理组件，提供地图图层的可视化管理界面
 * 
 * 该组件是地图应用的核心控制面板，提供以下功能：
 * - 显示地图中所有已加载的图层列表
 * - 控制图层的显示/隐藏状态
 * - 支持图层的拖拽排序，调整图层叠加顺序
 * - 提供图层样式编辑功能，可以修改点、线、面图层的样式
 * - 支持图层的添加和删除操作
 * - 提供图层样式的实时预览
 * 
 * 组件与mapLayerManager store紧密集成，通过store提供的API
 * 实现对OpenLayers地图图层的管理和控制。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 -->
<template>
  <div class="layer-manager-container">
    <!-- 添加标签页切换组件 -->
    <div class="layer-tabs">
      <el-tabs v-model="activeTab" class="layer-tab-container">
        <el-tab-pane label="已加载图层" name="loaded">
          <div class="panel-content">
            <!-- 已加载图层列表 -->
            <div v-if="loadedLayers.length === 0" class="empty-layer-message">
              暂无已加载图层
            </div>
            
            <el-tree
              v-else
              :data="loadedLayers"
              :props="{ label: 'name' }"
              node-key="id"
              default-expand-all
              draggable
              :allow-drop="allowDrop"
              @node-drag-end="handleDragEnd"
            >
              <template #default="{ node, data }">
                <div 
                  class="custom-tree-node" 
                  @contextmenu.prevent="showContextMenu($event, data)"
                >
                  <div class="node-content">
                    <!-- 修改样式按钮，仅矢量图层显示 -->
                    <div 
                      v-if="data.type === 'vector'" 
                      class="style-preview" 
                      @click.stop="openStyleEditor(data)"
                      :title="'修改样式'"
                    >
                      <!-- 针对不同几何类型的样式预览 -->
                      <!-- 点图层预览 -->
                      <div v-if="data.originalLayer.geometryType?.includes('Point')" class="point-preview">
                        <div class="point-inner" :style="getPointPreviewStyle(data)"></div>
                      </div>
                      
                      <!-- 线图层预览 -->
                      <div v-else-if="data.originalLayer.geometryType?.includes('Line')" class="line-preview">
                        <div class="line-inner" :style="getLinePreviewStyle(data)"></div>
                      </div>
                      
                      <!-- 面图层预览 -->
                      <div v-else class="polygon-preview">
                        <div class="polygon-inner" :style="getPolygonPreviewStyle(data)"></div>
                      </div>
                    </div>
                    
                    <!-- 用颜色点代替样式按钮，针对栅格图层 -->
                    <div v-else class="raster-indicator" :title="'栅格图层'">
                      <div class="raster-icon">
                        <div class="raster-grid"></div>
                      </div>
                    </div>
                    
                    <span class="layer-name" :title="node.label">{{ node.label }}</span>
                    <span class="layer-type" :title="getLayerTypeDisplay(data)">
                      {{ getLayerTypeDisplay(data) }}
                    </span>
                  </div>
                  <div class="node-actions">
                    <!-- 显示/隐藏按钮 -->
                    <el-tooltip content="显示/隐藏图层" placement="top">
                      <el-button 
                        type="primary" 
                        :icon="data.visible ? View : Hide"
                        circle 
                        size="small"
                        @click.stop="toggleLayerVisibility(data)"
                        :class="{'inactive-layer': !data.visible}"
                      />
                    </el-tooltip>
                    <!-- 删除按钮 -->
                    <el-tooltip content="删除图层" placement="top">
                      <el-button 
                        type="danger" 
                        :icon="Delete" 
                        circle 
                        size="small"
                        @click.stop="removeLayer(data)"
                      />
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="未加载图层" name="unloaded">
          <div class="panel-content">
            <!-- 未加载图层列表 -->
            <div v-if="unloadedLayers.length === 0" class="empty-layer-message">
              暂无未加载图层
            </div>
            
            <el-tree
              v-else
              :data="unloadedLayers"
              :props="{ label: 'name' }"
              node-key="id"
              default-expand-all
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <div class="node-content">
                    <!-- 用颜色点代替样式按钮 -->
                    <div 
                      :class="[
                        data.type === 'vector' ? 'vector-indicator' : 'raster-indicator',
                        'layer-type-indicator'
                      ]" 
                      :title="data.type === 'vector' ? '矢量图层' : '栅格图层'"
                    >
                      <div :class="[data.type === 'vector' ? 'vector-icon' : 'raster-icon']">
                        <div :class="[data.type === 'vector' ? 'vector-shape' : 'raster-grid']"></div>
                      </div>
                    </div>
                    
                    <span class="layer-name" :title="node.label">{{ node.label }}</span>
                    <span class="layer-type" :title="getLayerTypeDisplay(data)">
                      {{ getLayerTypeDisplay(data) }}
                    </span>
                  </div>
                  <div class="node-actions">
                    <!-- 添加图层按钮 -->
                    <el-tooltip content="加载图层" placement="top">
                      <el-button 
                        type="success" 
                        :icon="Plus" 
                        circle 
                        size="small"
                        @click.stop="addLayer(data)"
                      />
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 右键菜单 -->
    <div 
      v-show="contextMenu.visible" 
      class="context-menu"
      :style="{
        left: contextMenu.x + 'px',
        top: contextMenu.y + 'px'
      }"
    >
      <div 
        v-if="contextMenu.layer?.type === 'vector'" 
        class="menu-items"
      >
        <div class="menu-item" @click="zoomToLayer">
          <el-icon><ZoomIn /></el-icon>
          <span>缩放至图层</span>
        </div>
        <div class="menu-item" @click="showLabelDialog">
          <el-icon><Comment /></el-icon>
          <span>设置图层标注</span>
        </div>
      </div>
      <div v-else class="menu-items">
        <div class="menu-item" @click="zoomToLayer">
          <el-icon><ZoomIn /></el-icon>
          <span>缩放至图层</span>
        </div>
      </div>
    </div>
    
    <!-- 样式编辑弹窗 -->
    <el-dialog 
      v-model="styleDialog.visible" 
      title="修改图层样式" 
      width="400px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="style-editor-content">
        <template v-if="styleDialog.currentLayer">
          <h3>{{ styleDialog.currentLayer.name }}</h3>
          
          <!-- 通用属性：透明度 -->
          <el-form-item label="透明度">
            <el-slider 
              v-model="styleDialog.style.opacity" 
              :min="0" 
              :max="1" 
              :step="0.1"
              :format-tooltip="formatTooltip"
            ></el-slider>
          </el-form-item>
          
          <!-- 点图层特有属性 -->
          <div v-if="styleDialog.geometryType?.includes('Point')">
            <!-- 点大小 -->
            <el-form-item label="点大小">
              <el-input-number 
                v-model="styleDialog.style.radius" 
                :min="2" 
                :max="20" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 点边框粗细 -->
            <el-form-item label="边框粗细">
              <el-input-number 
                v-model="styleDialog.style.weight" 
                :min="0" 
                :max="5" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 点边框颜色 -->
            <el-form-item label="边框颜色">
              <el-color-picker v-model="styleDialog.style.color"></el-color-picker>
            </el-form-item>
            
            <!-- 点填充颜色 -->
            <el-form-item label="填充颜色">
              <el-color-picker v-model="styleDialog.style.fillColor" show-alpha></el-color-picker>
            </el-form-item>
          </div>
          
          <!-- 线图层特有属性 -->
          <div v-else-if="styleDialog.geometryType?.includes('Line')">
            <!-- 线条粗细 -->
            <el-form-item label="线条粗细">
              <el-input-number 
                v-model="styleDialog.style.weight" 
                :min="1" 
                :max="10" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 线条颜色 -->
            <el-form-item label="线条颜色">
              <el-color-picker v-model="styleDialog.style.color"></el-color-picker>
            </el-form-item>
            
            <!-- 线条样式 -->
            <el-form-item label="线条样式">
              <div class="custom-select">
                <div 
                  class="select-item" 
                  :class="{ 'active': Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.length === 0 }"
                  @click="styleDialog.style.lineDash = []"
                >
                  <div class="line-preview solid"></div>
                  <span>实线</span>
                </div>
                <div 
                  class="select-item" 
                  :class="{ 'active': Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.toString() === '4,8' }"
                  @click="styleDialog.style.lineDash = [4, 8]"
                >
                  <div class="line-preview dashed"></div>
                  <span>虚线</span>
                </div>
                <div 
                  class="select-item" 
                  :class="{ 'active': Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.toString() === '1,4,8,4' }"
                  @click="styleDialog.style.lineDash = [1, 4, 8, 4]"
                >
                  <div class="line-preview dotted"></div>
                  <span>点划线</span>
                </div>
              </div>
            </el-form-item>
          </div>
          
          <!-- 面图层特有属性 -->
          <div v-else>
            <!-- 线条粗细 -->
            <el-form-item label="边框粗细">
              <el-input-number 
                v-model="styleDialog.style.weight" 
                :min="0" 
                :max="10" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 线条颜色 -->
            <el-form-item label="边框颜色">
              <el-color-picker v-model="styleDialog.style.color"></el-color-picker>
            </el-form-item>
            
            <!-- 填充颜色 -->
            <el-form-item label="填充颜色">
              <el-color-picker v-model="styleDialog.style.fillColor" show-alpha></el-color-picker>
            </el-form-item>
          </div>
          
          <!-- 实时预览 -->
          <div class="style-preview-container">
            <h4>样式预览</h4>
            <div class="preview-box">
              <!-- 点预览 -->
              <div 
                v-if="styleDialog.geometryType?.includes('Point')" 
                class="point-preview-large"
              >
                <div class="point-inner" :style="{
                  width: styleDialog.style.radius * 2 + 'px',
                  height: styleDialog.style.radius * 2 + 'px',
                  backgroundColor: styleDialog.style.fillColor,
                  borderColor: styleDialog.style.color,
                  borderWidth: styleDialog.style.weight + 'px',
                  opacity: styleDialog.style.opacity
                }"></div>
              </div>
              
              <!-- 线预览 -->
              <div 
                v-else-if="styleDialog.geometryType?.includes('Line')" 
                class="line-preview-large"
              >
                <div class="line-inner" :style="{
                  borderTop: `${styleDialog.style.weight}px ${Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.length > 0 ? 
                    (styleDialog.style.lineDash.toString() === '4,8' ? 'dashed' : 'dotted') : 'solid'} ${styleDialog.style.color}`,
                  opacity: styleDialog.style.opacity
                }"></div>
              </div>
              
              <!-- 面预览 -->
              <div 
                v-else 
                class="polygon-preview-large"
              >
                <div class="polygon-inner" :style="{
                  backgroundColor: styleDialog.style.fillColor,
                  borderColor: styleDialog.style.color,
                  borderWidth: styleDialog.style.weight + 'px',
                  opacity: styleDialog.style.opacity
                }"></div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="styleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="applyStyle">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 标注设置弹窗 -->
    <el-dialog
      v-model="labelDialog.visible"
      title="设置图层标注"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="label-dialog-content">
        <p>选择要作为标注显示的属性：</p>
        
        <el-form>
          <el-form-item label="标注字段">
            <el-select v-model="labelDialog.selectedField" placeholder="请选择标注字段">
              <el-option
                v-for="field in labelDialog.availableFields"
                :key="field"
                :label="field"
                :value="field"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="标注颜色">
            <el-color-picker v-model="labelDialog.labelColor" />
          </el-form-item>
          
          <el-form-item label="字体大小">
            <el-input-number v-model="labelDialog.fontSize" :min="8" :max="24" size="small" />
          </el-form-item>
          
          
          
          <el-form-item label="最大分辨率">
            <el-tooltip content="当地图分辨率大于此值时，标注将不显示；值越小，标注消失得越早" placement="top">
              <el-input-number 
                v-model="labelDialog.maxLabelResolution" 
                :min="1" 
                :max="100" 
                size="small" 
                :step="1"
              />
            </el-tooltip>
          </el-form-item>
          <el-form-item label="显示标注">
            <el-switch v-model="labelDialog.showLabels" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="labelDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="applyLabels">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/mapLayer/mapLayerManager';
import { ElMessage, ElMessageBox } from 'element-plus';
import { View, Hide, Delete, Close, Brush, Refresh, ZoomIn, Comment, Plus } from '@element-plus/icons-vue';
import { Style, Fill, Stroke, Circle as CircleStyle, Text } from 'ol/style';
import { getCenter } from 'ol/extent';
import { MapLayer } from '/@/stores/mapLayer/types';
import Feature from 'ol/Feature';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: String,
    default: 'left',
    validator: (value: string) => ['left', 'right'].includes(value)
  },
  map: {
    type: Object,
    default: null
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'initialized']);

// 使用图层管理器
const mapLayerStore = useMapLayerManagerStore();

// 格式化透明度工具提示
const formatTooltip = (val: number): string => {
  return `${Math.round(val * 100)}%`;
};

// 获取图层类型显示文本
const getLayerTypeDisplay = (layer: any): string => {
  if (!layer) return '未知';
  
  if (layer.type === 'raster') {
    return '栅格';
  } else if (layer.type === 'vector') {
    // 确保 originalLayer 存在
    if (!layer.originalLayer) {
      return '矢量';
    }
    
    // 根据几何类型返回点、线、面
    const geometryType = layer.originalLayer.geometryType || '';
    if (geometryType.includes('Point')) {
      return '点';
    } else if (geometryType.includes('Line')) {
      return '线';
    } else if (geometryType.includes('Polygon')) {
      return '面';
    } else {
      return '矢量';
    }
  }
  return layer.type || '未知';
};

// 图层列表状态 - 使用响应式对象而非计算属性，以避免在toggle时自动更新顺序
const layerListState = reactive({
  items: [] as any[]
});

// 添加标签页状态
const activeTab = ref('loaded');

// 已加载图层列表
const loadedLayers = computed(() => {
  // 从 mapLayerStore 获取最新的已加载图层信息
  const layers = mapLayerStore.mapConfig?.layers || [];
  return layers.filter(layer => mapLayerStore.isLayerLoaded(layer.id)).map(layer => {
    // 返回包含所有必要属性的对象
    return {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      visible: true,  // 默认可见
      geometryType: layer.geometryType,
      originalLayer: layer  // 保留原始图层引用，确保能够访问到所有属性
    };
  });
});

// 未加载图层列表
const unloadedLayers = computed(() => {
  const layers = mapLayerStore.mapConfig?.layers || [];
  return layers.filter(layer => !mapLayerStore.isLayerLoaded(layer.id)).map(layer => {
    // 返回包含所有必要属性的对象
    return {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      originalLayer: layer  // 保留原始图层引用
    };
  });
});

// 监听图层变化
watch(() => mapLayerStore.loadedLayers, () => {
  // 当已加载图层列表发生变化时，更新组件状态
  layerListState.items = loadedLayers.value;
}, { deep: true });

// 样式编辑对话框状态
const styleDialog = reactive({
  visible: false,
  currentLayer: null as any,
  geometryType: null as string | null,
  style: {
    opacity: 1,
    weight: 2,
    color: '#3388ff',
    fillColor: 'rgba(51, 136, 255, 0.3)',
    radius: 6,
    lineDash: [] as number[]
  }
});

// 右键菜单状态
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  layer: null as any
});

// 标注设置对话框状态
const labelDialog = reactive({
  visible: false,
  currentLayer: null as any,
  availableFields: [] as string[],
  selectedField: '',
  labelColor: '#ffffff',
  fontSize: 12,
  showLabels: true,
  maxLabelResolution: 20  // 添加最大分辨率属性
});

// 监听visible属性变化
watch(() => props.visible, (newValue) => {
  if (newValue) {
    initLayerList();
  }
});

// 在组件挂载时初始化
onMounted(() => {
  // 初始化 layerListState.items
  layerListState.items = loadedLayers.value;
  
  // 添加事件监听器
  document.addEventListener('click', handleDocumentClick);
  
  // 监听图层加载状态变化
  if (props.map) {
    props.map.on('change:layers', () => {
      console.log('地图图层发生变化，刷新图层列表');
      initLayerList();
    });
  }
});

// 组件卸载前清理
onBeforeUnmount(() => {
  document.removeEventListener('click', handleDocumentClick);
});

// 处理文档点击事件，关闭右键菜单
function handleDocumentClick() {
  contextMenu.visible = false;
}

// 显示右键菜单
function showContextMenu(event: MouseEvent, layer: any) {
  // 阻止默认右键菜单
  event.preventDefault();
  
  // 检查图层是否存在
  if (!layer) {
    console.error('无法获取图层信息');
    return;
  }
  
  // 如果不是矢量图层，不显示右键菜单
  if (layer.type !== 'vector' && layer.type !== 'raster') {
    return;
  }
  
  // 获取容器元素
  const containerElement = document.querySelector('.layer-manager-container');
  if (!containerElement) {
    console.error('未找到容器元素');
    return;
  }
  
  // 计算相对于容器的位置
  const containerRect = containerElement.getBoundingClientRect();
  const relativeX = event.clientX - containerRect.left;
  const relativeY = event.clientY - containerRect.top;
  
  // 确保菜单不超出容器
  const menuWidth = 180; // 右键菜单宽度
  const menuHeight = 100; // 估计的右键菜单高度
  
  const adjustedX = Math.min(relativeX, containerRect.width - menuWidth);
  const adjustedY = Math.min(relativeY, containerRect.height - menuHeight);
  
  console.log('容器位置:', containerRect);
  console.log('鼠标位置:', { x: event.clientX, y: event.clientY });
  console.log('相对位置:', { x: relativeX, y: relativeY });
  console.log('调整后位置:', { x: adjustedX, y: adjustedY });
  
  // 设置菜单位置和当前图层
  contextMenu.x = adjustedX;
  contextMenu.y = adjustedY;
  contextMenu.layer = layer;
  contextMenu.visible = true;
  
  // 阻止事件冒泡，避免触发document的点击事件
  event.stopPropagation();
}

// 缩放至图层
function zoomToLayer() {
  if (!contextMenu.layer || !mapLayerStore.olMap) {
    return;
  }
  
  try {
    const layerId = contextMenu.layer.id;
    const layer = mapLayerStore.getLayerById(layerId);
    
    if (!layer || !layer.layerInstance) {
      ElMessage.warning('无法获取图层实例');
      return;
    }
    
    const source = layer.layerInstance.getSource();
    
    if (!source) {
      ElMessage.warning('无法获取图层数据源');
      return;
    }
    
    // 获取图层范围
    let extent;
    
    if (layer.type === 'vector') {
      // 对于 WFS 或矢量图层，检查要素是否已加载
      const features = source.getFeatures();
      
      if (features && features.length > 0) {
        // 如果有要素，从要素计算范围
        console.log(`图层 ${layer.name} 有 ${features.length} 个要素`);
        extent = source.getExtent();
      } else {
        // 如果没有要素，尝试从图层配置获取默认范围
        console.log(`图层 ${layer.name} 没有要素，尝试获取默认范围`);
        
        // 尝试从原始图层配置获取范围
        const originalLayer = layer.originalLayer || {};
        extent = originalLayer.extent;
        
        if (!extent && layer.protocol === 'WFS') {
          // 对于 WFS 图层，如果没有要素也没有配置的范围，加载一些要素
          console.log('尝试为 WFS 图层加载要素以获取范围');
          
          // 使用默认的中国大致范围作为视图中心
          const defaultCenter = [11808925, 4100000]; // 中国中部的大致经纬度转换为Web墨卡托坐标
          const defaultZoom = 5; // 缩放级别
          
          // 使用默认中心点和缩放级别
          const view = mapLayerStore.olMap.getView();
          view.animate({
            center: defaultCenter,
            duration: 1000,
            zoom: defaultZoom
          });
          
          ElMessage.info(`正在加载图层 ${layer.name} 的要素数据，请稍候`);
          
          // 让用户知道我们正在尝试加载数据
          setTimeout(() => {
            // 延迟后再次检查是否有要素可用
            const updatedFeatures = source.getFeatures();
            if (updatedFeatures && updatedFeatures.length > 0) {
              const updatedExtent = source.getExtent();
              if (updatedExtent && !updatedExtent.some((val: number) => !isFinite(val))) {
                view.fit(updatedExtent, {
                  padding: [50, 50, 50, 50],
                  duration: 1000
                });
                ElMessage.success(`已缩放至图层: ${contextMenu.layer.name}`);
              }
            }
          }, 2000);
          
          // 关闭右键菜单
          contextMenu.visible = false;
          return;
        }
      }
    } else if (layer.type === 'raster') {
      // 栅格图层，使用配置的范围
      const layerWithExtent = layer as any;
      extent = layerWithExtent.extent;
      
      // 如果没有配置范围，尝试从图层实例获取
      if (!extent && typeof source.getExtent === 'function') {
        extent = source.getExtent();
      }
      
      // 如果仍然没有范围，尝试从瓦片网格获取
      if (!extent && source.getTileGrid) {
        const tileGrid = source.getTileGrid();
        if (tileGrid) {
          extent = tileGrid.getExtent();
        }
      }
      
      // 如果仍然没有范围，使用默认的Web墨卡托范围
      if (!extent) {
        extent = [-20037508.34, -20037508.34, 20037508.34, 20037508.34]; // Web墨卡托默认范围
        console.log('使用默认Web墨卡托范围');
      }
    }
    
    // 检查范围是否有效
    if (!extent || extent.some((val: number) => !isFinite(val))) {
      console.warn('范围无效或为空:', extent);
      ElMessage.warning('无法获取图层范围，使用默认视图');
      
      // 使用默认的中国大致范围作为视图中心
      const defaultCenter = [11808925, 4100000]; 
      const defaultZoom = 5;
      
      // 使用默认中心点和缩放级别
      const view = mapLayerStore.olMap.getView();
      view.animate({
        center: defaultCenter,
        duration: 1000,
        zoom: defaultZoom
      });
    } else {
      // 范围有效，缩放到图层范围
      const view = mapLayerStore.olMap.getView();
      view.fit(extent, {
        padding: [50, 50, 50, 50],
        duration: 1000
      });
      
      ElMessage.success(`已缩放至图层: ${contextMenu.layer.name}`);
    }
    
    // 关闭右键菜单
    contextMenu.visible = false;
  } catch (error) {
    console.error('缩放至图层失败:', error);
    ElMessage.error('缩放至图层失败');
    
    // 关闭右键菜单
    contextMenu.visible = false;
  }
}

// 显示标注设置对话框
function showLabelDialog() {
  if (!contextMenu.layer || contextMenu.layer.type !== 'vector') {
    return;
  }
  
  try {
    const layerId = contextMenu.layer.id;
    const layer = mapLayerStore.getLayerById(layerId);
    
    if (!layer || !layer.layerInstance) {
      ElMessage.warning('无法获取图层实例');
      return;
    }
    
    // 设置当前图层
    labelDialog.currentLayer = contextMenu.layer;
    
    // 获取图层的第一个要素，用于提取属性字段
    const source = layer.layerInstance.getSource();
    const features = source.getFeatures();
    
    if (!features || features.length === 0) {
      ElMessage.warning('图层没有要素数据');
      return;
    }
    
    // 获取第一个要素的属性
    const properties = features[0].getProperties();
    
    // 过滤掉几何属性和内部属性
    const fields = Object.keys(properties).filter(key => 
      key !== 'geometry' && !key.startsWith('_')
    );
    
    if (fields.length === 0) {
      ElMessage.warning('图层要素没有可用的属性字段');
      return;
    }
    
    // 设置可用字段
    labelDialog.availableFields = fields;
    
    // 尝试获取当前的标注配置
    const layerWithLabels = layer as any;
    if (layerWithLabels.labelField) {
      labelDialog.selectedField = layerWithLabels.labelField;
      labelDialog.labelColor = layerWithLabels.labelStyle?.color || '#ffffff';
      labelDialog.fontSize = layerWithLabels.labelStyle?.fontSize || 24;
      labelDialog.showLabels = layerWithLabels.showLabels === true;
      labelDialog.maxLabelResolution = layerWithLabels.maxLabelResolution || 20; // 获取已保存的最大分辨率
    } else {
      // 默认选择第一个字段
      labelDialog.selectedField = fields[0];
      labelDialog.labelColor = '#ffffff';
      labelDialog.fontSize = 24;
      labelDialog.showLabels = false;
      labelDialog.maxLabelResolution = 100; // 默认值
    }
    
    // 显示对话框
    labelDialog.visible = true;
    
    // 关闭右键菜单
    contextMenu.visible = false;
  } catch (error) {
    console.error('打开标注设置对话框失败:', error);
    ElMessage.error('无法获取图层属性');
  }
}

// 应用标注设置
function applyLabels() {
  if (!labelDialog.currentLayer) return;
  
  try {
    const layerId = labelDialog.currentLayer.id;
    const layer = mapLayerStore.getLayerById(layerId);
    
    if (!layer || !layer.layerInstance) {
      ElMessage.error('图层不存在或未加载');
      return;
    }
    
    // 保存标注配置到图层
    const layerWithLabels = layer as any;
    layerWithLabels.labelField = labelDialog.selectedField;
    layerWithLabels.labelStyle = {
      color: labelDialog.labelColor,
      fontSize: labelDialog.fontSize
    };
    layerWithLabels.showLabels = labelDialog.showLabels;
    layerWithLabels.maxLabelResolution = labelDialog.maxLabelResolution; // 保存最大分辨率设置
    
    // 获取图层的当前样式
    const currentStyle = layer.layerInstance.getStyle();
    
    // 创建新的样式函数，使用闭包保存当前图层的标注设置
    const labelSettings = {
      field: labelDialog.selectedField,
      color: labelDialog.labelColor,
      fontSize: labelDialog.fontSize,
      showLabels: labelDialog.showLabels,
      maxLabelResolution: labelDialog.maxLabelResolution // 包含最大分辨率设置
    };
    
    const newStyleFunction = (feature: Feature, resolution: number) => {
      // 基础样式（可能是函数）
      let baseStyle = typeof currentStyle === 'function' 
        ? currentStyle(feature, resolution) 
        : currentStyle;
      
      // 如果是数组，取第一个样式
      if (Array.isArray(baseStyle)) {
        baseStyle = baseStyle[0];
      }
      
      // 如果不显示标注，直接返回基础样式
      if (!labelSettings.showLabels) {
        // 确保返回的样式没有文本
        if (baseStyle) {
          return new Style({
            image: baseStyle.getImage(),
            fill: baseStyle.getFill(),
            stroke: baseStyle.getStroke(),
            // 不设置text属性，这样就不会显示标注
          });
        }
        return baseStyle;
      }
      
      // 如果当前分辨率大于最大标签分辨率，不显示标签
      if (resolution > labelSettings.maxLabelResolution) {
        if (baseStyle) {
          return new Style({
            image: baseStyle.getImage(),
            fill: baseStyle.getFill(),
            stroke: baseStyle.getStroke()
          });
        }
        return baseStyle;
      }
      
      // 获取标注文本
      const labelValue = feature.get(labelSettings.field);
      if (labelValue === undefined || labelValue === null) {
        return baseStyle;
      }
      
      // 创建文本样式
      const textStyle = new Text({
        text: String(labelValue),
        font: `${labelSettings.fontSize}px sans-serif`,
        fill: new Fill({
          color: labelSettings.color
        }),
        stroke: new Stroke({
          color: '#000000',
          width: 2
        }),
        offsetY: -15,
        overflow: true
      });
      
      // 创建包含文本的新样式
      const newStyle = new Style({
        image: baseStyle.getImage(),
        fill: baseStyle.getFill(),
        stroke: baseStyle.getStroke(),
        text: textStyle
      });
      
      return newStyle;
    };
    
    // 应用新样式
    layer.layerInstance.setStyle(newStyleFunction);
    
    // 更新图层列表中的样式预览
    const treeNode = layerListState.items.find(item => item.id === layerId);
    if (treeNode && treeNode.originalLayer) {
      treeNode.originalLayer.labelField = labelSettings.field;
      treeNode.originalLayer.labelStyle = {
        color: labelSettings.color,
        fontSize: labelSettings.fontSize
      };
      treeNode.originalLayer.showLabels = labelSettings.showLabels;
      treeNode.originalLayer.maxLabelResolution = labelSettings.maxLabelResolution; // 更新最大分辨率
    }
    
    ElMessage.success('图层标注已更新');
    labelDialog.visible = false;
  } catch (error) {
    console.error('应用标注设置失败:', error);
    ElMessage.error('更新图层标注失败');
  }
}

// 初始化图层列表
function initLayerList() {
  if (!mapLayerStore.mapConfig) {
    layerListState.items = [];
    return;
  }
  
  // 创建图层项数组，按照zIndex降序排列（zIndex高的在前面/上层）
  const layers = [...mapLayerStore.mapConfig.layers]
    .filter(layer => mapLayerStore.loadedLayers.includes(layer.id));
    
  // 按图层的显示顺序排序（查找图层的OL实例，获取zIndex）
  layers.sort((a, b) => {
    const aZIndex = a.layerInstance?.getZIndex() || 0;
    const bZIndex = b.layerInstance?.getZIndex() || 0;
    return bZIndex - aZIndex; // 降序，高zIndex在前
  });
  
  // 映射为树节点数据
  layerListState.items = layers.map(layer => {
    // 获取完整的图层信息
    const fullLayer = mapLayerStore.getLayerById(layer.id);
    let geometryType = fullLayer?.geometryType;
    
    // 提取样式信息
    let defaultStyle = fullLayer?.defaultStyle;
    
    // 如果defaultStyle是字符串（样式名称），从styleLibrary获取
    if (typeof defaultStyle === 'string') {
      defaultStyle = mapLayerStore.styleLibrary[defaultStyle] || {};
    }
    
    // 对于矢量图层，如果没有指定几何类型，尝试从图层实例的第一个要素中提取
    if (layer.type === 'vector' && !geometryType && layer.layerInstance) {
      // 尝试获取第一个要素的几何类型
      const source = layer.layerInstance.getSource();
      if (source && typeof source.getFeatures === 'function') {
        const features = source.getFeatures();
        if (features && features.length > 0) {
          // 获取第一个要素的几何类型
          const geometry = features[0].getGeometry();
          if (geometry) {
            geometryType = geometry.getType();
          }
        }
      }
    }
    
    return {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      visible: layer.active || false,
      icon: getLayerIcon(layer.type),
      originalLayer: {
        ...fullLayer,
        geometryType: geometryType || fullLayer?.geometryType,
        defaultStyle: defaultStyle || fullLayer?.defaultStyle
      }
    };
  });
  
  console.log('图层列表已初始化，共', layerListState.items.length, '项');
  emit('initialized', layerListState.items.length);
}

// 更新单个图层的状态
function updateLayerItem(layerId: string, properties: Partial<typeof layerListState.items[0]>) {
  const item = layerListState.items.find(item => item.id === layerId);
  if (item) {
    Object.assign(item, properties);
  }
}

// 根据图层类型获取图标
function getLayerIcon(type: string) {
  switch(type) {
    case 'vector': return 'el-icon-picture-outline';
    case 'raster': return 'el-icon-picture';
    default: return 'el-icon-map-location';
  }
}

// 获取点图层预览样式
function getPointPreviewStyle(layer: any) {
  // 确保layer和layer.originalLayer存在
  if (!layer || !layer.originalLayer) {
    return {
      backgroundColor: 'rgba(51, 136, 255, 0.6)',
      borderColor: '#3388ff',
      borderWidth: '1px',
      opacity: 1
    };
  }

  // 尝试从originalLayer获取style
  let style = layer.originalLayer.defaultStyle || {};
  
  // 如果在originalLayer中没有样式或样式为字符串，尝试从mapLayerManager重新获取
  if (!style || typeof style === 'string') {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    style = originalLayer?.defaultStyle || {};
    
    // 如果依然是字符串（样式名称），则从styleLibrary获取
    if (typeof style === 'string') {
      style = mapLayerStore.styleLibrary[style] || {};
    }
  }
  
  return {
    backgroundColor: style.fillColor || 'rgba(51, 136, 255, 0.6)',
    borderColor: style.color || '#3388ff',
    borderWidth: (style.weight || 1) + 'px',
    opacity: style.opacity !== undefined ? style.opacity : 1
  };
}

// 获取线图层预览样式
function getLinePreviewStyle(layer: any) {
  // 确保layer和layer.originalLayer存在
  if (!layer || !layer.originalLayer) {
    return {
      backgroundColor: 'transparent',
      borderTop: '2px solid #3388ff',
      opacity: 1
    };
  }

  // 尝试从originalLayer获取style
  let style = layer.originalLayer.defaultStyle || {};
  
  // 如果在originalLayer中没有样式或样式为字符串，尝试从mapLayerManager重新获取
  if (!style || typeof style === 'string') {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    style = originalLayer?.defaultStyle || {};
    
    // 如果依然是字符串（样式名称），则从styleLibrary获取
    if (typeof style === 'string') {
      style = mapLayerStore.styleLibrary[style] || {};
    }
  }
  
  // 判断线型
  let borderStyle = 'solid';
  if (style.hasOwnProperty('lineDash') && Array.isArray((style as any).lineDash)) {
    const lineDash = (style as any).lineDash;
    if (lineDash.length > 0) {
      if (lineDash.toString() === '4,8') {
        borderStyle = 'dashed';
      } else if (lineDash.toString() === '1,4,8,4') {
        borderStyle = 'dotted';
      }
    }
  }
  
  return {
    backgroundColor: 'transparent',
    borderTop: `${style.weight || 2}px ${borderStyle} ${style.color || '#3388ff'}`,
    opacity: style.opacity !== undefined ? style.opacity : 1
  };
}

// 获取面图层预览样式
function getPolygonPreviewStyle(layer: any) {
  // 确保layer和layer.originalLayer存在
  if (!layer || !layer.originalLayer) {
    return {
      backgroundColor: 'rgba(51, 136, 255, 0.3)',
      borderColor: '#3388ff',
      borderWidth: '2px',
      opacity: 1
    };
  }

  // 尝试从originalLayer获取style
  let style = layer.originalLayer.defaultStyle || {};
  
  // 如果在originalLayer中没有样式或样式为字符串，尝试从mapLayerManager重新获取
  if (!style || typeof style === 'string') {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    style = originalLayer?.defaultStyle || {};
    
    // 如果依然是字符串（样式名称），则从styleLibrary获取
    if (typeof style === 'string') {
      style = mapLayerStore.styleLibrary[style] || {};
    }
  }
  
  return {
    backgroundColor: style.fillColor || 'rgba(51, 136, 255, 0.3)',
    borderColor: style.color || '#3388ff',
    borderWidth: (style.weight || 2) + 'px',
    opacity: style.opacity !== undefined ? style.opacity : 1
  };
}

// 切换图层可见性
function toggleLayerVisibility(node: any) {
  const success = mapLayerStore.toggleLayerVisibility(node.id);
  if (success !== null) {
    // 只更新当前节点的可见性，不重建整个列表
    node.visible = success;
    ElMessage({
      type: "success",
      message: `图层 ${node.name} 已${success ? '显示' : '隐藏'}`
    });
  } else {
    ElMessage.error(`切换图层 ${node.name} 可见性失败`);
  }
}

// 删除图层
function removeLayer(layer: any) {
  ElMessageBox.confirm(
    `确定要删除图层 "${layer.name}" 吗？`,
    '删除图层',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 调用store的删除图层方法
    const success = mapLayerStore.removeLayer(layer.id);
    
    if (success) {
      ElMessage.success(`成功删除图层: ${layer.name}`);
      // 刷新图层列表
      initLayerList();
    } else {
      ElMessage.error(`删除图层失败: ${layer.name}`);
    }
  }).catch(() => {
    // 用户取消删除
  });
}

// 只允许同级拖拽，不允许成为子节点
function allowDrop(draggingNode: any, dropNode: any, type: string) {
  return type === 'prev' || type === 'next';
}

// 处理拖拽结束事件，调整图层顺序
function handleDragEnd(draggingNode: any, dropNode: any, dropType: string, ev: any) {
  // 获取拖拽后的顺序（树组件已经更新了DOM顺序）
  const newOrder = layerListState.items.map(node => node.id);
  
  // 使用图层管理器的重排方法更新地图中的图层顺序
  if (mapLayerStore.olMap) {
    const successCount = mapLayerStore.reorderLayers(newOrder);
    
    if (successCount > 0) {
      ElMessage.success(`图层顺序已调整，${successCount} 个图层已更新`);
    } else {
      ElMessage.warning('图层顺序调整失败');
    }
  }
}

// 打开样式编辑器
function openStyleEditor(layer: any) {
  if (!layer) {
    ElMessage.error('图层信息不完整，无法编辑样式');
    return;
  }

  styleDialog.currentLayer = layer;
  
  // 首先尝试从传入的layer.originalLayer获取样式信息
  let currentStyle: any = {};
  
  // 获取当前图层的样式和几何类型
  if (layer.originalLayer && layer.originalLayer.defaultStyle) {
    if (typeof layer.originalLayer.defaultStyle === 'string') {
      // 如果样式是字符串引用，尝试从styleLibrary获取
      currentStyle = mapLayerStore.styleLibrary[layer.originalLayer.defaultStyle] || {};
    } else {
      // 直接使用对象样式
      currentStyle = layer.originalLayer.defaultStyle;
    }
  }
  
  // 如果从layer中没有获取到样式，再尝试从mapLayerManager获取
  if (Object.keys(currentStyle).length === 0) {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    
    if (originalLayer?.defaultStyle) {
      if (typeof originalLayer.defaultStyle === 'string') {
        // 如果样式是字符串引用，尝试从styleLibrary获取
        currentStyle = mapLayerStore.styleLibrary[originalLayer.defaultStyle] || {};
      } else {
        // 直接使用对象样式
        currentStyle = originalLayer.defaultStyle;
      }
    }
  }
  
  console.log('打开样式编辑器，当前样式:', currentStyle);
  
  // 设置几何类型，优先使用传入的layer中的信息
  let geometryType = '';
  if (layer.originalLayer && layer.originalLayer.geometryType) {
    geometryType = layer.originalLayer.geometryType;
  } else {
    const layerFromStore = mapLayerStore.getLayerById(layer.id);
    if (layerFromStore && layerFromStore.geometryType) {
      geometryType = layerFromStore.geometryType;
    } else {
      geometryType = 'Polygon'; // 默认值
    }
  }
  
  styleDialog.geometryType = geometryType;
  
  // 为不同几何类型准备默认样式
  if (styleDialog.geometryType?.includes('Line')) {
    // 线图层专有样式设置
    styleDialog.style = {
      opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
      weight: currentStyle.weight || 2,
      color: currentStyle.color || '#3388ff',
      fillColor: '', // 线不需要填充颜色
      radius: 0, // 线不需要半径
      lineDash: Array.isArray(currentStyle.lineDash) ? [...currentStyle.lineDash] : []
    };
  } else if (styleDialog.geometryType?.includes('Point')) {
    // 点图层样式设置
    styleDialog.style = {
      opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
      weight: currentStyle.weight || 1,
      color: currentStyle.color || '#3388ff',
      fillColor: currentStyle.fillColor || 'rgba(51, 136, 255, 0.3)',
      radius: currentStyle.radius || 6,
      lineDash: []
    };
  } else {
    // 面图层样式设置
    styleDialog.style = {
      opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
      weight: currentStyle.weight || 2,
      color: currentStyle.color || '#3388ff',
      fillColor: currentStyle.fillColor || 'rgba(51, 136, 255, 0.3)',
      radius: 0,
      lineDash: []
    };
  }
  
  styleDialog.visible = true;
}

// 应用样式
function applyStyle() {
  if (!styleDialog.currentLayer) return;
  
  try {
    // 更新图层样式
    const layerId = styleDialog.currentLayer.id;
    const newStyle = { ...styleDialog.style };
    
    // 检查图层实例是否存在
    const layer = mapLayerStore.getLayerById(layerId);
    if (layer && layer.layerInstance) {
      let olStyle;
      
      if (styleDialog.geometryType?.includes('Point')) {
        // 点图层样式
        olStyle = new Style({
          image: new CircleStyle({
            radius: newStyle.radius,
            fill: new Fill({
              color: newStyle.fillColor
            }),
            stroke: new Stroke({
              color: newStyle.color,
              width: newStyle.weight
            })
          })
        });
      } else if (styleDialog.geometryType?.includes('Line')) {
        // 线图层样式 - 安全处理lineDash
        const lineDash = Array.isArray(newStyle.lineDash) ? [...newStyle.lineDash] : [];
        
        olStyle = new Style({
          stroke: new Stroke({
            color: newStyle.color,
            width: newStyle.weight,
            lineDash: lineDash
          })
        });
        
        // 线图层应该只保留相关属性
        newStyle.fillColor = '';
        newStyle.radius = 0;
      } else {
        // 面图层样式
        olStyle = new Style({
          stroke: new Stroke({
            color: newStyle.color,
            width: newStyle.weight
          }),
          fill: new Fill({
            color: newStyle.fillColor
          })
        });
        
        // 面图层没有lineDash属性
        newStyle.lineDash = [];
        newStyle.radius = 0;
      }
      
      // 更新样式
      if (typeof layer.layerInstance.setStyle === 'function') {
        layer.layerInstance.setStyle(olStyle);
        
        // 更新原始图层的样式配置 - 确保不同图层类型只保存需要的属性
        let updatedStyle;
        
        if (styleDialog.geometryType?.includes('Line')) {
          // 线图层只保存需要的属性
          updatedStyle = {
            opacity: newStyle.opacity,
            weight: newStyle.weight,
            color: newStyle.color,
            lineDash: Array.isArray(newStyle.lineDash) ? [...newStyle.lineDash] : []
          };
        } else if (styleDialog.geometryType?.includes('Point')) {
          // 点图层属性
          updatedStyle = {
            opacity: newStyle.opacity,
            weight: newStyle.weight,
            color: newStyle.color,
            fillColor: newStyle.fillColor,
            radius: newStyle.radius
          };
        } else {
          // 面图层属性
          updatedStyle = {
            opacity: newStyle.opacity,
            weight: newStyle.weight,
            color: newStyle.color,
            fillColor: newStyle.fillColor
          };
        }
        
        // 更新图层样式
        layer.defaultStyle = updatedStyle as any; // 使用类型断言解决lineDash属性不在类型定义中的问题
        
        // 更新当前树节点的样式预览
        const treeNode = layerListState.items.find(item => item.id === layerId);
        if (treeNode && treeNode.originalLayer) {
          // 避免引用同一个对象，创建新对象赋值
          treeNode.originalLayer.defaultStyle = { ...updatedStyle };
        }
        
        ElMessage.success('图层样式已更新');
      } else {
        ElMessage.warning('该图层不支持样式设置');
      }
    } else {
      ElMessage.error('图层不存在或未加载');
    }
    
    styleDialog.visible = false;
  } catch (error) {
    console.error('应用样式时出错:', error);
    ElMessage.error('更新样式失败');
  }
}

// 加载图层方法
async function addLayer(layer: any) {
  try {
    // 显示加载中提示
    ElMessage.info({
      message: `正在加载图层: ${layer.name}`,
      duration: 1000
    });
    
    // 调用store的添加图层方法
    const success = await mapLayerStore.addLayer(layer.id);
    
    if (success) {
      ElMessage.success(`成功加载图层: ${layer.name}`);
      // 刷新图层列表
      initLayerList();
    } else {
      ElMessage.error(`加载图层失败: ${layer.name}`);
    }
  } catch (error) {
    console.error('加载图层出错:', error);
    ElMessage.error(`加载图层出错: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 修改暴露给父组件的方法
defineExpose({
  // 不再需要 initLayerList 方法
  refresh: () => {
    // 强制更新图层列表
    layerListState.items = loadedLayers.value;
  }
});
</script>

<style lang="scss" scoped>
.layer-manager-container {
  height: 100%;
  background-color: rgba(2, 49, 52, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.layer-tabs {
  height: 100%;
  
  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .el-tabs__header {
      margin: 0;
      background: rgba(255, 255, 255, 0.05);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      .el-tabs__nav {
        border: none;
        width: 100%;
        display: flex;
        
        .el-tabs__item {
          flex: 1;
          text-align: center;
          color: rgba(255, 255, 255, 0.7);
          height: 40px;
          line-height: 40px;
          border: none;
          transition: all 0.3s;
          
          &.is-active {
            color: #fff;
            background: rgba(42, 119, 41, 0.3);
          }
          
          &:hover {
            color: #fff;
          }
        }
      }
    }
    
    .el-tabs__content {
      flex: 1;
      overflow: auto;
      padding: 10px;
    }
  }
}

.panel-content {
  height: 100%;
}

.empty-layer-message {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  padding: 20px;
}

.custom-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: all 0.3s;
  margin: 6px 0;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.node-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.layer-name {
  color: #fff;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.layer-type {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.node-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.style-preview, .layer-type-indicator {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
}

.point-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .point-inner {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid #2a7729;
    background-color: rgba(42, 119, 41, 0.3);
  }
}

.line-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 4px;
  
  .line-inner {
    width: 100%;
    border-top: 2px solid #2a7729;
  }
}

.polygon-preview {
  width: 100%;
  height: 100%;
  padding: 4px;
  
  .polygon-inner {
    width: 100%;
    height: 100%;
    border: 2px solid #2a7729;
    background-color: rgba(42, 119, 41, 0.3);
    border-radius: 2px;
  }
}

.raster-indicator {
  .raster-icon {
    width: 16px;
    height: 16px;
    position: relative;
    overflow: hidden;
    border-radius: 2px;
    
    .raster-grid {
      width: 100%;
      height: 100%;
      background-image: linear-gradient(to right, rgba(255, 255, 255, 0.5) 1px, transparent 1px),
                        linear-gradient(to bottom, rgba(255, 255, 255, 0.5) 1px, transparent 1px);
      background-size: 6px 6px;
    }
  }
}

.vector-indicator {
  .vector-icon {
    width: 16px;
    height: 16px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .vector-shape {
      width: 10px;
      height: 10px;
      border: 2px solid rgba(255, 255, 255, 0.7);
      border-radius: 2px;
    }
  }
}

:deep(.el-tree) {
  background-color: transparent;
  
  .el-tree-node__content {
    background-color: transparent;
    margin-bottom: 30px;
    
    &:hover {
      background-color: transparent;
    }
  }
  
  .el-tree-node__children {
    padding-left: 0;
  }
  
  .el-tree-node__expand-icon {
    display: none;
  }
}

:deep(.el-button) {
  &.is-circle {
    width: 28px;
    height: 28px;
    padding: 0;
    border-radius: 6px;
    
    .el-icon {
      font-size: 16px;
    }
  }
  
  &.el-button--primary {
    --el-button-bg-color: #2a7729;
    --el-button-border-color: #2a7729;
    --el-button-hover-bg-color: #358a34;
    --el-button-hover-border-color: #358a34;
    --el-button-active-bg-color: #1e5a1d;
    --el-button-active-border-color: #1e5a1d;
  }
  
  &.el-button--success {
    --el-button-bg-color: #2a7729;
    --el-button-border-color: #2a7729;
    --el-button-hover-bg-color: #358a34;
    --el-button-hover-border-color: #358a34;
    --el-button-active-bg-color: #1e5a1d;
    --el-button-active-border-color: #1e5a1d;
  }
  
  &.el-button--danger {
    --el-button-bg-color: #d84315;
    --el-button-border-color: #d84315;
    --el-button-hover-bg-color: #e65100;
    --el-button-hover-border-color: #e65100;
    --el-button-active-bg-color: #bf360c;
    --el-button-active-border-color: #bf360c;
  }
  
  &.inactive-layer {
    opacity: 0.5;
  }
}

.context-menu {
  position: absolute;
  background-color: rgba(2, 49, 52, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  min-width: 180px;
  padding: 6px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-items {
  display: flex;
  flex-direction: column;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.2s;
  
  &:hover {
    background-color: rgba(42, 119, 41, 0.8);
    color: #fff;
  }
  
  .el-icon {
    font-size: 16px;
  }
}

/* 标注设置对话框样式 */
.label-dialog-content {
  padding: 16px 0;
  
  p {
    margin-bottom: 16px;
    color: rgba(255, 255, 255, 0.9);
  }
}
</style> 