import{I as m,q as y,__tla as _}from"./index.BSP3cg_z.js";import{d as c,l as f,A as w,w as L,B as v,a as g,b as h,f as i,t as b,G as q,u as x,v as I}from"./vue.CnN__PXn.js";let d,$=Promise.all([(()=>{try{return _}catch{}})()]).then(async()=>{let s,l,o,e,u;s={class:"layout-padding layout-link-container"},l={class:"layout-padding-auto layout-padding-view"},o={class:"layout-link-warp"},e={class:"layout-link-msg"},u=c({name:"layoutLinkView"}),d=y(c({...u,setup(j){const t=f(),a=w({title:"",isLink:""}),r=()=>{m(a.isLink)?window.open(a.isLink):window.open(`${a.isLink}`)};return L(()=>t.path,()=>{a.title=t.name,a.isLink=t.meta.isLink},{immediate:!0}),(p,n)=>{const k=v("el-button");return h(),g("div",s,[i("div",l,[i("div",o,[n[1]||(n[1]=i("i",{class:"layout-link-icon iconfont icon-xingqiu"},null,-1)),i("div",e,'\u9875\u9762 "'+q(p.$t(x(a).title))+'" \u5DF2\u5728\u65B0\u7A97\u53E3\u4E2D\u6253\u5F00',1),b(k,{class:"mt30",round:"",onClick:r},{default:I(()=>n[0]||(n[0]=[i("i",{class:"iconfont icon-lianjie"},null,-1),i("span",null,"\u7ACB\u5373\u524D\u5F80\u4F53\u9A8C",-1)])),_:1})])])])}}}),[["__scopeId","data-v-f4265247"]])});export{$ as __tla,d as default};
