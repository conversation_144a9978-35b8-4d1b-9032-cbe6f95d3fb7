#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建指定数量的任务文件夹和TaskInfo.json文件的脚本
文件夹名称格式：YYYYMMDDHHMMSSFF (年月日时分秒微秒)
时间范围：2024年到2026年随机
"""

import os
import json
import random
from datetime import datetime, timedelta
import uuid
import argparse


def generate_random_timestamp():
    """生成2024-2026年之间的随机时间戳"""
    # 设置时间范围
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2026, 12, 31, 23, 59, 59)
    
    # 计算时间差
    time_between = end_date - start_date
    days_between = time_between.days
    
    # 生成随机天数
    random_days = random.randrange(days_between)
    random_seconds = random.randrange(24 * 60 * 60)  # 一天内的随机秒数
    random_microseconds = random.randrange(100)  # 0-99的微秒
    
    # 生成随机时间
    random_date = start_date + timedelta(days=random_days, seconds=random_seconds)
    
    # 格式化为文件夹名称格式：YYYYMMDDHHMMSSFF
    folder_name = random_date.strftime("%Y%m%d%H%M%S") + f"{random_microseconds:02d}"
    
    return folder_name, random_date


def create_task_info_json(task_id, folder_path):
    """创建TaskInfo.json文件内容"""
    
    # 生成随机的UUID用于task_id
    tif_task_id = str(uuid.uuid4())
    geoserver_task_id = str(uuid.uuid4())
    
    # 生成随机的时间戳用于各个时间字段
    base_time = datetime.now()
    start_time = base_time - timedelta(minutes=random.randint(1, 10))
    odm_start = start_time + timedelta(seconds=random.randint(1, 5))
    odm_end = odm_start + timedelta(seconds=random.randint(1, 3))
    tif_start = odm_end + timedelta(seconds=random.randint(10, 20))
    tif_end = tif_start + timedelta(minutes=1, seconds=random.randint(0, 30))
    geo_start = tif_end + timedelta(seconds=random.randint(1, 5))
    geo_end = geo_start + timedelta(minutes=1, seconds=random.randint(0, 30))
    map_start = geo_end + timedelta(seconds=random.randint(1, 5))
    map_end = map_start + timedelta(seconds=random.randint(1, 3))
    
    # 生成随机的bbox坐标（基于原始数据的范围）
    base_minx = 107.5222275926538
    base_miny = 22.480316091584275
    base_maxx = 107.52630906252625
    base_maxy = 22.483774283861617
    
    # 添加一些随机偏移
    offset_x = random.uniform(-0.01, 0.01)
    offset_y = random.uniform(-0.01, 0.01)
    
    minx = base_minx + offset_x
    miny = base_miny + offset_y
    maxx = base_maxx + offset_x
    maxy = base_maxy + offset_y
    
    # 根据task_id生成年月主题
    year = task_id[:4]
    month = task_id[4:6]
    theme = f"{year}年{month}月"
    
    task_info = {
        "Task": [
            {
                "id": task_id,
                "startTime": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "endTime": map_end.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "完成",
                "odm_process": {
                    "task_id": "",
                    "status": "完成",
                    "start_time": odm_start.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": odm_end.strftime("%Y-%m-%d %H:%M:%S"),
                    "retry_count": 0,
                    "log_file": "",
                    "task_list": []
                },
                "tif_process": {
                    "task_id": tif_task_id,
                    "status": "完成",
                    "start_time": tif_start.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": tif_end.strftime("%Y-%m-%d %H:%M:%S"),
                    "retry_count": 0,
                    "log_file": f"D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\tiflog\\{tif_task_id}.log",
                    "task_list": [
                        tif_task_id
                    ]
                },
                "geoserver_publish": {
                    "task_id": geoserver_task_id,
                    "status": "完成",
                    "start_time": geo_start.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": geo_end.strftime("%Y-%m-%d %H:%M:%S"),
                    "retry_count": 0,
                    "layer_name": f"testodm:{task_id}",
                    "task_list": [
                        geoserver_task_id
                    ],
                    "log_file": f"D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\geolog\\{geoserver_task_id}.log"
                },
                "map_config": {
                    "status": "完成",
                    "start_time": map_start.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": map_end.strftime("%Y-%m-%d %H:%M:%S"),
                    "bbox": {
                        "minx": minx,
                        "miny": miny,
                        "maxx": maxx,
                        "maxy": maxy,
                        "crs": "EPSG:4326"
                    },
                    "layer_id": f"testodm:{task_id}",
                    "theme": theme
                },
                "input_path": f"/opt/airflow/data/ODM/Input/{task_id}/project",
                "output_path": f"/opt/airflow/data/ODM/Output/{task_id}",
                "airflow_input_path": f"/opt/airflow/data/ODM/Input/{task_id}/project",
                "airflow_output_path": f"/opt/airflow/data/ODM/Output/{task_id}",
                "window_input_path": f"D:/Drone_Project/nginxData/ODM/Input/{task_id}/project",
                "window_output_path": f"D:/Drone_Project/nginxData/ODM/Output/{task_id}"
            }
        ]
    }
    
    # 写入JSON文件
    json_file_path = os.path.join(folder_path, "TaskInfo.json")
    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump(task_info, f, ensure_ascii=False, indent=4)
    
    return json_file_path


def create_task_folders(base_directory, num_folders):
    """创建指定数量的任务文件夹"""
    
    # 确保基础目录存在
    if not os.path.exists(base_directory):
        os.makedirs(base_directory)
        print(f"创建基础目录: {base_directory}")
    
    created_folders = []
    used_names = set()
    
    for i in range(num_folders):
        # 生成唯一的文件夹名称
        while True:
            folder_name, timestamp = generate_random_timestamp()
            if folder_name not in used_names:
                used_names.add(folder_name)
                break
        
        # 创建文件夹路径
        folder_path = os.path.join(base_directory, folder_name)
        
        try:
            # 创建文件夹
            os.makedirs(folder_path, exist_ok=True)
            
            # 创建TaskInfo.json文件
            json_file = create_task_info_json(folder_name, folder_path)
            
            created_folders.append({
                'folder_name': folder_name,
                'folder_path': folder_path,
                'json_file': json_file,
                'timestamp': timestamp
            })
            
            print(f"创建完成 [{i+1}/{num_folders}]: {folder_name} -> {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            print(f"创建文件夹 {folder_name} 时出错: {str(e)}")
    
    return created_folders


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='创建任务文件夹和TaskInfo.json文件')
    parser.add_argument('-d', '--directory', default='./task_folders', 
                       help='基础目录路径 (默认: ./task_folders)')
    parser.add_argument('-n', '--number', type=int, default=10,
                       help='要创建的文件夹数量 (默认: 10)')
    
    args = parser.parse_args()
    
    print(f"开始创建 {args.number} 个任务文件夹...")
    print(f"基础目录: {os.path.abspath(args.directory)}")
    print("-" * 50)
    
    # 创建文件夹
    created_folders = create_task_folders(args.directory, args.number)
    
    print("-" * 50)
    print(f"任务完成！成功创建了 {len(created_folders)} 个文件夹")
    
    # 显示创建的文件夹统计信息
    if created_folders:
        years = {}
        for folder in created_folders:
            year = folder['folder_name'][:4]
            years[year] = years.get(year, 0) + 1
        
        print("\n按年份统计:")
        for year, count in sorted(years.items()):
            print(f"  {year}年: {count} 个文件夹")


if __name__ == "__main__":
    main()
