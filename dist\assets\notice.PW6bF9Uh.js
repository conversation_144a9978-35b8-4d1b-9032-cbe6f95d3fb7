import{s as a,__tla as u}from"./index.BSP3cg_z.js";let e,s,r,n,i,o,d=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{n=function(t){return a({url:"/admin/sysNotice/page",method:"get",params:t})},e=function(t){return a({url:"/admin/sysNotice",method:"post",data:t})},i=function(t){return a({url:"/admin/sysNotice/details",method:"get",params:t})},r=function(t){return a({url:"/admin/sysNotice",method:"delete",data:t})},o=function(t){return a({url:"/admin/sysNotice",method:"put",data:t})},s=function(t){return a({url:"/admin/sysNotice/lists",method:"get",params:t})}});export{d as __tla,e as a,s as b,r as d,n as f,i as g,o as p};
