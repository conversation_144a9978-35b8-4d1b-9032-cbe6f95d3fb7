import{s as G,q as I,__tla as X}from"./index.BSP3cg_z.js";import{i as P}from"./echarts.DrVj8Jfx.js";import{d as j,k as h,A as Y,B as Z,a as J,b as K,f as e,Y as r,G as d,u as s,t as S,v as R,Z as L}from"./vue.CnN__PXn.js";let q,N=Promise.all([(()=>{try{return X}catch{}})()]).then(async()=>{let n,v,f,x,c,m,w,b,p,g,u,y,k,F,C,z,_,M,B,A,E,D,H;n={class:"layout-padding"},v={class:"layout-padding-auto layout-padding-view"},f={class:"p-4 min-w-[375px] md:min-w-[700px] xl:min-w-[800px] mt-3 grid grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-3 3xl:grid-cols-6"},x={class:"relative flex flex-grow !flex-row flex-col items-center rounded-[10px] border-[1px] border-gray-200 bg-blue-50 hover:scale-105 hover:shadow-lg bg-clip-border shadow-md shadow-[#F3F3F3] dark:border-[#ffffff33] dark:!bg-navy-800 dark:text-white dark:shadow-none"},c={class:"h-50 ml-4 flex w-auto flex-col justify-center"},m={class:"text-xl font-bold text-navy-700 dark:text-white"},w={class:"relative flex flex-grow !flex-row flex-col items-center rounded-[10px] border-[1px] border-gray-200 bg-blue-50 hover:scale-105 hover:shadow-lg bg-clip-border shadow-md shadow-[#F3F3F3] dark:border-[#ffffff33] dark:!bg-navy-800 dark:text-white dark:shadow-none"},b={class:"h-50 ml-4 flex w-auto flex-col justify-center"},p={class:"text-xl font-bold text-navy-700 dark:text-white"},g={class:"relative flex flex-grow !flex-row flex-col items-center rounded-[10px] border-[1px] border-gray-200 bg-blue-50 hover:scale-105 hover:shadow-lg bg-clip-border shadow-md shadow-[#F3F3F3] dark:border-[#ffffff33] dark:!bg-navy-800 dark:text-white dark:shadow-none"},u={class:"h-50 ml-4 flex w-auto flex-col justify-center"},y={class:"text-xl font-bold text-navy-700 dark:text-white"},k={class:"relative flex flex-grow !flex-row flex-col items-center rounded-[10px] border-[1px] border-gray-200 bg-blue-50 hover:scale-105 hover:shadow-lg bg-clip-border shadow-md shadow-[#F3F3F3] dark:border-[#ffffff33] dark:!bg-navy-800 dark:text-white dark:shadow-none"},F={class:"h-50 ml-4 flex w-auto flex-col justify-center"},C={class:"text-xl font-bold text-navy-700 dark:text-white"},z={class:"relative flex flex-grow !flex-row flex-col items-center rounded-[10px] border-[1px] border-gray-200 bg-blue-50 hover:scale-105 hover:shadow-lg bg-clip-border shadow-md shadow-[#F3F3F3] dark:border-[#ffffff33] dark:!bg-navy-800 dark:text-white dark:shadow-none"},_={class:"h-50 ml-4 flex w-auto flex-col justify-center"},M={class:"text-xl font-bold text-navy-700 dark:text-white"},B={class:"relative flex flex-grow !flex-row items-center rounded-[10px] border-[1px] border-gray-200 bg-blue-50 hover:scale-105 hover:shadow-lg bg-clip-border shadow-md shadow-[#F3F3F3] dark:border-[#ffffff33] dark:!bg-navy-800 dark:text-white dark:shadow-none"},A={class:"h-50 ml-4 flex w-auto flex-col justify-center"},E={class:"text-xl font-bold text-navy-700 dark:text-white"},D={class:"sm:flex"},H=j({name:"cache"}),q=I(j({...H,setup(Q){const a=h({}),O=h(),V=h(),l=Y({commandChartOption:{tooltip:{trigger:"item"},series:[{label:{show:!0},labelLine:{show:!0},type:"pie",radius:"85%",color:["#0D47A1","#1565C0","#1976D2","#1E88E5","#2196F3","#42A5F5","#64B5F6","#90CAF9","#BBDEFB","#E3F2FD","#CAF0F8","#ADE8F4","#90E0EF","#48CAE4","#00B4D8","#0096C7","#0077B6","#023E8A","#03045E","#8ecae6","#98c1d9","#D9ED92","#B5E48C","#99D98C","#76C893","#52B69A","#34A0A4","#168AAD","#1A759F","#1E6091","#184E77","#457b9d"],data:[{value:"",name:""}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]},memoryChartOption:{tooltip:{formatter:"{a} <br/>{b} : {c}%"},series:[{name:"Pressure",type:"gauge",radius:"100%",detail:{formatter:"{value}"},data:[{value:"",name:"\u5185\u5B58\u6D88\u8017"}]}]}});return(async()=>{const o=await G({url:"/admin/system/cache",method:"get"});a.value=o.data.info,a.value.dbSize=o.data.dbSize,l.commandChartOption.series[0].data=o.data.commandStats,l.memoryChartOption.series[0].data[0].value=(o.data.info.used_memory/1024/1024).toFixed(2),l.memoryChartOption.series[0].detail.formatter="{value}M";const t=L(P(O.value)),i=L(P(V.value));t.setOption(l.commandChartOption),i.setOption(l.memoryChartOption)})(),(o,t)=>{const i=Z("el-card");return K(),J("div",n,[e("div",v,[e("div",f,[e("div",x,[t[1]||(t[1]=r('<div class="ml-[18px] flex h-[90px] w-auto flex-row items-center" data-v-e327b736><div class="rounded-full bg-lightPrimary p-3 dark:bg-navy-700" data-v-e327b736><span class="flex items-center text-brand-500 dark:text-white" data-v-e327b736><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="h-7 w-7" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg" data-v-e327b736><path fill="none" d="M0 0h24v24H0z" data-v-e327b736></path><path d="M4 9h4v11H4zM16 13h4v7h-4zM10 4h4v16h-4z" data-v-e327b736></path></svg></span></div></div>',1)),e("div",c,[t[0]||(t[0]=e("p",{class:"font-dm text-sm font-medium text-gray-600"},"Redis\u7248\u672C",-1)),e("h4",m,d(s(a).redis_version),1)])]),e("div",w,[t[3]||(t[3]=r('<div class="ml-[18px] flex h-[90px] w-auto flex-row items-center" data-v-e327b736><div class="rounded-full bg-lightPrimary p-3 dark:bg-navy-700" data-v-e327b736><span class="flex items-center text-brand-500 dark:text-white" data-v-e327b736><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg" data-v-e327b736><path d="M298.39 248a4 4 0 002.86-6.8l-78.4-79.72a4 4 0 00-6.85 2.81V236a12 12 0 0012 12z" data-v-e327b736></path><path d="M197 267a43.67 43.67 0 01-13-31v-92h-72a64.19 64.19 0 00-64 64v224a64 64 0 0064 64h144a64 64 0 0064-64V280h-92a43.61 43.61 0 01-31-13zm175-147h70.39a4 4 0 002.86-6.8l-78.4-79.72a4 4 0 00-6.85 2.81V108a12 12 0 0012 12z" data-v-e327b736></path><path d="M372 152a44.34 44.34 0 01-44-44V16H220a60.07 60.07 0 00-60 60v36h42.12A40.81 40.81 0 01231 124.14l109.16 111a41.11 41.11 0 0111.83 29V400h53.05c32.51 0 58.95-26.92 58.95-60V152z" data-v-e327b736></path></svg></span></div></div>',1)),e("div",b,[t[2]||(t[2]=e("p",{class:"font-dm text-sm font-medium text-gray-600"},"\u5BA2\u6237\u7AEF\u6570",-1)),e("h4",p,d(s(a).connected_clients),1)])]),e("div",g,[t[5]||(t[5]=r('<div class="ml-[18px] flex h-[90px] w-auto flex-row items-center" data-v-e327b736><div class="rounded-full bg-lightPrimary p-3 dark:bg-navy-700" data-v-e327b736><span class="flex items-center text-brand-500 dark:text-white" data-v-e327b736><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="h-7 w-7" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg" data-v-e327b736><path fill="none" d="M0 0h24v24H0z" data-v-e327b736></path><path d="M4 9h4v11H4zM16 13h4v7h-4zM10 4h4v16h-4z" data-v-e327b736></path></svg></span></div></div>',1)),e("div",u,[t[4]||(t[4]=e("p",{class:"font-dm text-sm font-medium text-gray-600"},"\u8FD0\u884C\u65F6\u95F4(\u5929)",-1)),e("h4",y,d(s(a).uptime_in_days),1)])]),e("div",k,[t[7]||(t[7]=r('<div class="ml-[18px] flex h-[90px] w-auto flex-row items-center" data-v-e327b736><div class="rounded-full bg-lightPrimary p-3 dark:bg-navy-700" data-v-e327b736><span class="flex items-center text-brand-500 dark:text-white" data-v-e327b736><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg" data-v-e327b736><path fill="none" d="M0 0h24v24H0z" data-v-e327b736></path><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" data-v-e327b736></path></svg></span></div></div>',1)),e("div",F,[t[6]||(t[6]=e("p",{class:"font-dm text-sm font-medium text-gray-600"},"\u4F7F\u7528\u5185\u5B58",-1)),e("h4",C,d(s(a).used_memory_human),1)])]),e("div",z,[t[9]||(t[9]=r('<div class="ml-[18px] flex h-[90px] w-auto flex-row items-center" data-v-e327b736><div class="rounded-full bg-lightPrimary p-3 dark:bg-navy-700" data-v-e327b736><span class="flex items-center text-brand-500 dark:text-white" data-v-e327b736><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="h-7 w-7" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg" data-v-e327b736><path fill="none" d="M0 0h24v24H0z" data-v-e327b736></path><path d="M4 9h4v11H4zM16 13h4v7h-4zM10 4h4v16h-4z" data-v-e327b736></path></svg></span></div></div>',1)),e("div",_,[t[8]||(t[8]=e("p",{class:"font-dm text-sm font-medium text-gray-600"},"AOF\u662F\u5426\u5F00\u542F",-1)),e("h4",M,d(s(a).aof_enabled==0?"\u5F00\u542F":"\u5173\u95ED"),1)])]),e("div",B,[t[11]||(t[11]=r('<div class="ml-[18px] flex h-[90px] w-auto flex-row items-center" data-v-e327b736><div class="rounded-full bg-lightPrimary p-3 dark:bg-navy-700" data-v-e327b736><span class="flex items-center text-brand-500 dark:text-white" data-v-e327b736><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg" data-v-e327b736><path d="M208 448V320h96v128h97.6V256H464L256 64 48 256h62.4v192z" data-v-e327b736></path></svg></span></div></div>',1)),e("div",A,[t[10]||(t[10]=e("p",{class:"font-dm text-sm font-medium text-gray-600"},"RDB\u662F\u5426\u6210\u529F",-1)),e("h4",E,d(s(a).aof_enabled=="ok"?"\u6210\u529F":"\u5931\u8D25"),1)])])]),e("div",D,[S(i,{class:"sm:mr-4 flex-1 !border-none mt-4",shadow:"never"},{default:R(()=>[e("div",null,[t[12]||(t[12]=e("div",{class:"mb-10 font-semibold"},"\u547D\u4EE4\u7EDF\u8BA1",-1)),e("div",{class:"flex h-[30vh] items-center",ref_key:"commandChartRef",ref:O},null,512)])]),_:1}),S(i,{class:"flex-1 !border-none mt-4",shadow:"never"},{default:R(()=>[e("div",null,[t[13]||(t[13]=e("div",{class:"mb-10 font-semibold"},"\u5185\u5B58\u4FE1\u606F",-1)),e("div",{class:"flex h-[30vh] items-center",ref_key:"memoryChartRef",ref:V},null,512)])]),_:1})])])])}}}),[["__scopeId","data-v-e327b736"]])});export{N as __tla,q as default};
