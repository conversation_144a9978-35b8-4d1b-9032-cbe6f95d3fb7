import{L as n,k as u,M as o}from"./vue.CnN__PXn.js";import{i as h,__tla as d}from"./dict.D9OX-VAS.js";let i,v=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{const l=n("dict",{state:()=>({dict:[]}),actions:{getDict(a){try{const e=this.dict.find(t=>t.key===a);return e?e.value:null}catch{return null}},setDict(a,e){a&&typeof a=="string"&&this.dict.push({key:a,value:e})},removeDict(a){try{const e=this.dict.findIndex(t=>t.key===a);if(e!==-1)return this.dict.splice(e,1),!0}catch{return!1}return!1}}});i=function(...a){const e=u({});return a.forEach(t=>{e.value[t]=[];const c=l().getDict(t);c?e.value[t]=c:h(t).then(r=>{e.value[t]=r.data.map(s=>({label:s.label,value:s.value,elTagType:s.listClass,elTagClass:s.cssClass})),l().setDict(t,e.value[t])})}),o(e.value)}});export{v as __tla,i as u};
