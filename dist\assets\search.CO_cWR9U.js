import{w as Q,v as C,q as b,__tla as A}from"./index.BSP3cg_z.js";import{d as w,s as B,k as O,z as P,A as R,B as o,a as U,b as $,t as l,v as c,u as m,f as q,E as z,G as E,y as G}from"./vue.CnN__PXn.js";let S,H=Promise.all([(()=>{try{return A}catch{}})()]).then(async()=>{let d,h;d={class:"layout-search-dialog"},h=w({name:"layoutBreadcrumbSearch"}),S=b(w({...h,setup(M,{expose:_}){const y=Q(),{tagsViewRoutes:V}=B(y),p=O(),{t:g}=C.useI18n(),f=P(),s=R({isShowSearch:!1,menuQuery:"",tagsViewList:[]}),L=(e,a)=>{a(e?s.tagsViewList.filter(v(e)):s.tagsViewList)},v=e=>a=>a.path.toLowerCase().indexOf(e.toLowerCase())>-1||g(a.name).indexOf(e.toLowerCase())>-1,x=()=>{if(s.tagsViewList.length>0)return!1;V.value.map(e=>{var a;(a=e.meta)!=null&&a.isHide||s.tagsViewList.push({...e})})},k=e=>{var r,u,n;let{path:a,redirect:i}=e;(r=e.meta)!=null&&r.isLink&&!((u=e.meta)!=null&&u.isIframe)?window.open((n=e.meta)==null?void 0:n.isLink):i?f.push(i):f.push(a),s.isShowSearch=!1};return _({openSearch:()=>{s.menuQuery="",s.isShowSearch=!0,x(),G(()=>{setTimeout(()=>{p.value.focus()})})}}),(e,a)=>{const i=o("ele-Search"),r=o("el-icon"),u=o("SvgIcon"),n=o("el-autocomplete"),I=o("el-dialog");return $(),U("div",d,[l(I,{modelValue:m(s).isShowSearch,"onUpdate:modelValue":a[1]||(a[1]=t=>m(s).isShowSearch=t),"destroy-on-close":"","show-close":!1},{footer:c(()=>[l(n,{modelValue:m(s).menuQuery,"onUpdate:modelValue":a[0]||(a[0]=t=>m(s).menuQuery=t),"fetch-suggestions":L,placeholder:e.$t("user.searchPlaceholder"),ref_key:"layoutMenuAutocompleteRef",ref:p,onSelect:k,"fit-input-width":!0},{prefix:c(()=>[l(r,{class:"el-input__icon"},{default:c(()=>[l(i)]),_:1})]),default:c(({item:t})=>[q("div",null,[l(u,{name:t.meta.icon,class:"mr5"},null,8,["name"]),z(" "+E(e.$t(t.name)),1)])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-0035e1ed"]])});export{H as __tla,S as default};
