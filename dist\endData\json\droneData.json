{"code": 0, "data": [{"id": "UAV001", "drone_type": "多旋翼", "brand": "大疆", "power_type": "电池", "pilot_name": "张伟"}, {"id": "UAV002", "drone_type": "固定翼", "brand": "<PERSON><PERSON><PERSON>", "power_type": "燃油", "pilot_name": "李娜"}, {"id": "UAV003", "drone_type": "垂直起降", "brand": "<PERSON><PERSON><PERSON>", "power_type": "混合动力", "pilot_name": "王刚"}, {"id": "UAV004", "drone_type": "单旋翼", "brand": "Autel Robotics", "power_type": "电池", "pilot_name": "赵敏"}, {"id": "UAV005", "drone_type": "多旋翼", "brand": "Skydio", "power_type": "电池", "pilot_name": "孙悟空"}, {"id": "UAV052", "drone_type": "固定翼", "brand": "<PERSON><PERSON><PERSON>", "power_type": "燃油", "pilot_name": "李娜"}, {"id": "UAV053", "drone_type": "垂直起降", "brand": "<PERSON><PERSON><PERSON>", "power_type": "混合动力", "pilot_name": "王刚"}, {"id": "UAV054", "drone_type": "单旋翼", "brand": "Autel Robotics", "power_type": "电池", "pilot_name": "赵敏"}, {"id": "UAV055", "drone_type": "多旋翼", "brand": "Skydio", "power_type": "电池", "pilot_name": "孙悟空"}], "msg": ""}