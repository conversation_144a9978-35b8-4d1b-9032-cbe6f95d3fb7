import{d as E,s as N,l as j,z as P,A as R,c as T,o as H,O as J,B as b,a as l,D as d,b as r,t as p,u as n,v as h,T as K,F as U,p as W,e as v,G as y,Q as X,E as Y}from"./vue.CnN__PXn.js";import{t as Z,u as aa,o as f,L as V,q as ea,__tla as ta}from"./index.BSP3cg_z.js";let M,sa=Promise.all([(()=>{try{return ta}catch{}})()]).then(async()=>{let g,L,S,_,k,I;g={key:0,class:"layout-navbars-breadcrumb"},L={key:0,class:"layout-navbars-breadcrumb-span"},S={key:1},_={key:2},k=["onClick"],I=E({name:"layoutBreadcrumb"}),M=ea(E({...I,setup(ra){const z=Z(),$=aa(),{themeConfig:u}=N($),{routesList:C}=N(z),i=j(),F=P(),a=R({breadcrumbList:[],routeSplit:[],routeSplitFirst:"",routeSplitIndex:1}),q=T(()=>{c(i);const{layout:e,isBreadcrumb:s}=u.value;return e!=="classic"&&e!=="transverse"&&!!s}),A=()=>{u.value.isCollapse=!u.value.isCollapse,D()},D=()=>{V.remove("themeConfig"),V.set("themeConfig",u.value)},x=e=>{e.forEach(s=>{a.routeSplit.forEach((o,B,m)=>{a.routeSplitFirst===s.path&&(a.routeSplitFirst+=`/${m[a.routeSplitIndex]}`,a.breadcrumbList.push(s),a.routeSplitIndex++,s.children&&x(s.children))})})},c=e=>{let s=e.path;if(!u.value.isBreadcrumb)return!1;a.breadcrumbList=[C.value[0]],a.routeSplit=s.split("/"),a.routeSplit.shift(),a.routeSplitFirst=`/${a.routeSplit[0]}`,a.routeSplitIndex=1,x(C.value),a.breadcrumbList.push(i),e.name==="router.home"||e.name==="staticRoutes.notFound"&&a.breadcrumbList.length>1?a.breadcrumbList.splice(0,a.breadcrumbList.length-1):a.breadcrumbList.length>0&&(a.breadcrumbList[a.breadcrumbList.length-1].meta.tagsViewName=f.setMenuI18n(i))};return H(()=>{c(i)}),J(e=>{c(e)}),(e,s)=>{const o=b("SvgIcon"),B=b("el-breadcrumb-item"),m=b("el-breadcrumb");return q.value?(r(),l("div",g,[p(o,{class:"layout-navbars-breadcrumb-icon",name:n(u).isCollapse?"ele-Expand":"ele-Fold",size:16,onClick:A},null,8,["name"]),p(m,{class:"layout-navbars-breadcrumb-hide"},{default:h(()=>[p(K,{name:"breadcrumb"},{default:h(()=>[(r(!0),l(U,null,W(a.breadcrumbList,(t,G)=>(r(),v(B,{key:t.meta.tagsViewName?t.meta.tagsViewName:t.name},{default:h(()=>[G===a.breadcrumbList.length-1?(r(),l("span",L,[n(u).isBreadcrumbIcon?(r(),v(o,{key:0,name:t.meta.icon,class:"layout-navbars-breadcrumb-iconfont"},null,8,["name"])):d("",!0),t.meta.tagsViewName?(r(),l("div",_,y(t.meta.tagsViewName),1)):(r(),l("div",S,y(n(f).setMenuI18n(t)),1))])):(r(),l("a",{key:1,onClick:X(ua=>(O=>{const{redirect:w,path:Q}=O;w?F.push(w):F.push(Q)})(t),["prevent"])},[n(u).isBreadcrumbIcon?(r(),v(o,{key:0,name:t.meta.icon,class:"layout-navbars-breadcrumb-iconfont"},null,8,["name"])):d("",!0),Y(y(n(f).setMenuI18n(t)),1)],8,k))]),_:2},1024))),128))]),_:1})]),_:1})])):d("",!0)}}}),[["__scopeId","data-v-f1d950fd"]])});export{sa as __tla,M as default};
