import{v as A,c as h,r as v,__tla as E}from"./index.BSP3cg_z.js";import{g as G,p as H,a as P,d as R,__tla as z}from"./dept.B9Hc3gR-.js";import{d as I,k as i,A as J,B as n,m as K,a as M,b as V,t as r,v as o,q as N,e as Q,u as l,f as W,E as $,G as x,H as X,y as Y}from"./vue.CnN__PXn.js";let O,Z=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return z}catch{}})()]).then(async()=>{let f,b,y;f={class:"system-dept-dialog-container"},b={class:"dialog-footer"},y=I({name:"systemDeptDialog"}),O=I({...y,emits:["refresh"],setup(ee,{expose:T,emit:w}){const k=w,{t:B}=A.useI18n(),m=i(),t=J({parentId:"",deptId:"",name:"",sortOrder:9999}),c=i([]),u=i(!1),p=i(!1),q=i({parentId:[{required:!0,message:"\u4E0A\u7EA7\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{validator:v.overLength,trigger:"blur"},{required:!0,message:"\u90E8\u95E8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sortOrder:[{validator:v.overLength,trigger:"blur"},{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),D=async()=>{if(!await m.value.validate().catch(()=>{}))return!1;try{p.value=!0,t.deptId?await H(t):await P(t),h().success(B(t.deptId?"common.editSuccessText":"common.addSuccessText")),u.value=!1,k("refresh")}catch(e){h().error(e.msg)}finally{p.value=!1}},U=async()=>{R().then(e=>{c.value=[];const a={id:"0",name:"\u6839\u90E8\u95E8",children:[]};a.children=e.data,c.value.push(a)})};return T({openDialog:(e,a)=>{u.value=!0,t.deptId="",Y(()=>{var d;(d=m.value)==null||d.resetFields(),t.parentId=a}),e==="edit"&&G(a).then(d=>{Object.assign(t,d.data)}).catch(d=>{h().error(d.msg)}),U()}}),(e,a)=>{const d=n("el-tree-select"),g=n("el-form-item"),C=n("el-input"),F=n("el-input-number"),L=n("el-form"),_=n("el-button"),S=n("el-dialog"),j=K("loading");return V(),M("div",f,[r(S,{title:l(t).deptId?e.$t("common.editBtn"):e.$t("common.addBtn"),modelValue:l(u),"onUpdate:modelValue":a[4]||(a[4]=s=>X(u)?u.value=s:null),width:"600"},{footer:o(()=>[W("span",b,[r(_,{onClick:a[3]||(a[3]=s=>u.value=!1)},{default:o(()=>[$(x(e.$t("common.cancelButtonText")),1)]),_:1}),r(_,{type:"primary",onClick:D,disabled:l(p)},{default:o(()=>[$(x(e.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:o(()=>[N((V(),Q(L,{ref_key:"deptDialogFormRef",ref:m,model:l(t),"label-width":"90px",rules:l(q)},{default:o(()=>[r(g,{label:e.$t("sysdept.parentId"),prop:"parentId"},{default:o(()=>[r(d,{modelValue:l(t).parentId,"onUpdate:modelValue":a[0]||(a[0]=s=>l(t).parentId=s),data:l(c),props:{value:"id",label:"name",children:"children"},class:"w100",clearable:"","check-strictly":"","render-after-expand":!1,placeholder:e.$t("sysdept.inputparentIdTip")},null,8,["modelValue","data","placeholder"])]),_:1},8,["label"]),r(g,{label:e.$t("sysdept.name"),prop:"name"},{default:o(()=>[r(C,{modelValue:l(t).name,"onUpdate:modelValue":a[1]||(a[1]=s=>l(t).name=s),placeholder:e.$t("sysdept.inputnameTip"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(g,{label:e.$t("sysdept.sortOrder"),prop:"sortOrder"},{default:o(()=>[r(F,{modelValue:l(t).sortOrder,"onUpdate:modelValue":a[2]||(a[2]=s=>l(t).sortOrder=s),placeholder:e.$t("sysdept.inputsortOrderTip"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[j,l(p)]])]),_:1},8,["title","modelValue"])])}}})});export{Z as __tla,O as default};
