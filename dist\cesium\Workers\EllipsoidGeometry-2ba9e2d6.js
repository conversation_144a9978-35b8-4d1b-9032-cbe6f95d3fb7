define(["exports","./Transforms-01e95659","./Matrix2-7146c9ca","./Matrix3-a348023f","./ComponentDatatype-77274976","./defaultValue-0a909f67","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryOffsetAttribute-04332ce7","./IndexDatatype-2149f06c","./Math-e97915da","./VertexFormat-ab2e00e6"],(function(t,e,a,n,i,r,o,s,m,u,c,l){"use strict";const f=new n.Cartesian3,d=new n.Cartesian3,C=new n.Cartesian3,p=new n.Cartesian3,y=new n.Cartesian3,_=new n.Cartesian3(1,1,1),h=Math.cos,x=Math.sin;function A(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT);const e=r.defaultValue(t.radii,_),a=r.defaultValue(t.innerRadii,e),i=r.defaultValue(t.minimumClock,0),o=r.defaultValue(t.maximumClock,c.CesiumMath.TWO_PI),s=r.defaultValue(t.minimumCone,0),m=r.defaultValue(t.maximumCone,c.CesiumMath.PI),u=Math.round(r.defaultValue(t.stackPartitions,64)),f=Math.round(r.defaultValue(t.slicePartitions,64)),d=r.defaultValue(t.vertexFormat,l.VertexFormat.DEFAULT);this._radii=n.Cartesian3.clone(e),this._innerRadii=n.Cartesian3.clone(a),this._minimumClock=i,this._maximumClock=o,this._minimumCone=s,this._maximumCone=m,this._stackPartitions=u,this._slicePartitions=f,this._vertexFormat=l.VertexFormat.clone(d),this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidGeometry"}A.packedLength=2*n.Cartesian3.packedLength+l.VertexFormat.packedLength+7,A.pack=function(t,e,a){return a=r.defaultValue(a,0),n.Cartesian3.pack(t._radii,e,a),a+=n.Cartesian3.packedLength,n.Cartesian3.pack(t._innerRadii,e,a),a+=n.Cartesian3.packedLength,l.VertexFormat.pack(t._vertexFormat,e,a),a+=l.VertexFormat.packedLength,e[a++]=t._minimumClock,e[a++]=t._maximumClock,e[a++]=t._minimumCone,e[a++]=t._maximumCone,e[a++]=t._stackPartitions,e[a++]=t._slicePartitions,e[a]=r.defaultValue(t._offsetAttribute,-1),e};const k=new n.Cartesian3,b=new n.Cartesian3,w=new l.VertexFormat,P={radii:k,innerRadii:b,vertexFormat:w,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};let g;A.unpack=function(t,e,a){e=r.defaultValue(e,0);const i=n.Cartesian3.unpack(t,e,k);e+=n.Cartesian3.packedLength;const o=n.Cartesian3.unpack(t,e,b);e+=n.Cartesian3.packedLength;const s=l.VertexFormat.unpack(t,e,w);e+=l.VertexFormat.packedLength;const m=t[e++],u=t[e++],c=t[e++],f=t[e++],d=t[e++],C=t[e++],p=t[e];return r.defined(a)?(a._radii=n.Cartesian3.clone(i,a._radii),a._innerRadii=n.Cartesian3.clone(o,a._innerRadii),a._vertexFormat=l.VertexFormat.clone(s,a._vertexFormat),a._minimumClock=m,a._maximumClock=u,a._minimumCone=c,a._maximumCone=f,a._stackPartitions=d,a._slicePartitions=C,a._offsetAttribute=-1===p?void 0:p,a):(P.minimumClock=m,P.maximumClock=u,P.minimumCone=c,P.maximumCone=f,P.stackPartitions=d,P.slicePartitions=C,P.offsetAttribute=-1===p?void 0:p,new A(P))},A.createGeometry=function(t){const l=t._radii;if(l.x<=0||l.y<=0||l.z<=0)return;const _=t._innerRadii;if(_.x<=0||_.y<=0||_.z<=0)return;const A=t._minimumClock,k=t._maximumClock,b=t._minimumCone,w=t._maximumCone,P=t._vertexFormat;let g,v,F=t._slicePartitions+1,V=t._stackPartitions+1;F=Math.round(F*Math.abs(k-A)/c.CesiumMath.TWO_PI),V=Math.round(V*Math.abs(w-b)/c.CesiumMath.PI),F<2&&(F=2),V<2&&(V=2);let M=0;const T=[b],D=[A];for(g=0;g<V;g++)T.push(b+g*(w-b)/(V-1));for(T.push(w),v=0;v<F;v++)D.push(A+v*(k-A)/(F-1));D.push(k);const G=T.length,L=D.length;let O=0,I=1;const E=_.x!==l.x||_.y!==l.y||_.z!==l.z;let z=!1,N=!1,R=!1;E&&(I=2,b>0&&(z=!0,O+=F-1),w<Math.PI&&(N=!0,O+=F-1),(k-A)%c.CesiumMath.TWO_PI?(R=!0,O+=2*(V-1)+1):O+=1);const U=L*G*I,S=new Float64Array(3*U),B=new Array(U).fill(!1),W=new Array(U).fill(!1),Y=F*V*I,J=6*(Y+O+1-(F+V)*I),X=u.IndexDatatype.createTypedArray(Y,J),Z=P.normal?new Float32Array(3*U):void 0,j=P.tangent?new Float32Array(3*U):void 0,q=P.bitangent?new Float32Array(3*U):void 0,H=P.st?new Float32Array(2*U):void 0,K=new Array(G),Q=new Array(G);for(g=0;g<G;g++)K[g]=x(T[g]),Q[g]=h(T[g]);const $=new Array(L),tt=new Array(L);for(v=0;v<L;v++)tt[v]=h(D[v]),$[v]=x(D[v]);for(g=0;g<G;g++)for(v=0;v<L;v++)S[M++]=l.x*K[g]*tt[v],S[M++]=l.y*K[g]*$[v],S[M++]=l.z*Q[g];let et,at,nt,it,rt=U/2;if(E)for(g=0;g<G;g++)for(v=0;v<L;v++)S[M++]=_.x*K[g]*tt[v],S[M++]=_.y*K[g]*$[v],S[M++]=_.z*Q[g],B[rt]=!0,g>0&&g!==G-1&&0!==v&&v!==L-1&&(W[rt]=!0),rt++;for(M=0,g=1;g<G-2;g++)for(et=g*L,at=(g+1)*L,v=1;v<L-2;v++)X[M++]=at+v,X[M++]=at+v+1,X[M++]=et+v+1,X[M++]=at+v,X[M++]=et+v+1,X[M++]=et+v;if(E){const t=G*L;for(g=1;g<G-2;g++)for(et=t+g*L,at=t+(g+1)*L,v=1;v<L-2;v++)X[M++]=at+v,X[M++]=et+v,X[M++]=et+v+1,X[M++]=at+v,X[M++]=et+v+1,X[M++]=at+v+1}if(E){if(z)for(it=G*L,g=1;g<L-2;g++)X[M++]=g,X[M++]=g+1,X[M++]=it+g+1,X[M++]=g,X[M++]=it+g+1,X[M++]=it+g;if(N)for(nt=G*L-L,it=G*L*I-L,g=1;g<L-2;g++)X[M++]=nt+g+1,X[M++]=nt+g,X[M++]=it+g,X[M++]=nt+g+1,X[M++]=it+g,X[M++]=it+g+1}if(R){for(g=1;g<G-2;g++)it=L*G+L*g,nt=L*g,X[M++]=it,X[M++]=nt+L,X[M++]=nt,X[M++]=it,X[M++]=it+L,X[M++]=nt+L;for(g=1;g<G-2;g++)it=L*G+L*(g+1)-1,nt=L*(g+1)-1,X[M++]=nt+L,X[M++]=it,X[M++]=nt,X[M++]=nt+L,X[M++]=it+L,X[M++]=it}const ot=new s.GeometryAttributes;P.position&&(ot.position=new o.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:S}));let st=0,mt=0,ut=0,ct=0;const lt=U/2;let ft;const dt=n.Ellipsoid.fromCartesian3(l),Ct=n.Ellipsoid.fromCartesian3(_);if(P.st||P.normal||P.tangent||P.bitangent){for(g=0;g<U;g++){ft=B[g]?Ct:dt;const t=n.Cartesian3.fromArray(S,3*g,f),e=ft.geodeticSurfaceNormal(t,d);if(W[g]&&n.Cartesian3.negate(e,e),P.st){const t=a.Cartesian2.negate(e,y);H[st++]=Math.atan2(t.y,t.x)/c.CesiumMath.TWO_PI+.5,H[st++]=Math.asin(e.z)/Math.PI+.5}if(P.normal&&(Z[mt++]=e.x,Z[mt++]=e.y,Z[mt++]=e.z),P.tangent||P.bitangent){const t=C;let a,i=0;if(B[g]&&(i=lt),a=!z&&g>=i&&g<i+2*L?n.Cartesian3.UNIT_X:n.Cartesian3.UNIT_Z,n.Cartesian3.cross(a,e,t),n.Cartesian3.normalize(t,t),P.tangent&&(j[ut++]=t.x,j[ut++]=t.y,j[ut++]=t.z),P.bitangent){const a=n.Cartesian3.cross(e,t,p);n.Cartesian3.normalize(a,a),q[ct++]=a.x,q[ct++]=a.y,q[ct++]=a.z}}}P.st&&(ot.st=new o.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:H})),P.normal&&(ot.normal=new o.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Z})),P.tangent&&(ot.tangent=new o.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),P.bitangent&&(ot.bitangent=new o.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:q}))}if(r.defined(t._offsetAttribute)){const e=S.length,a=t._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(e/3).fill(a);ot.applyOffset=new o.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new o.Geometry({attributes:ot,indices:X,primitiveType:o.PrimitiveType.TRIANGLES,boundingSphere:e.BoundingSphere.fromEllipsoid(dt),offsetAttribute:t._offsetAttribute})},A.getUnitEllipsoid=function(){return r.defined(g)||(g=A.createGeometry(new A({radii:new n.Cartesian3(1,1,1),vertexFormat:l.VertexFormat.POSITION_ONLY}))),g},t.EllipsoidGeometry=A}));
