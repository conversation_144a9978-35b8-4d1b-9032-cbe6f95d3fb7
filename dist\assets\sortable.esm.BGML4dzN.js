function mt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,o)}return n}function K(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?mt(Object(n),!0).forEach(function(o){jt(e,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mt(Object(n)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(n,o))})}return e}function et(e){return et=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},et(e)}function jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function te(){return te=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},te.apply(this,arguments)}function Wt(e,t){if(e==null)return{};var n,o,i=function(r,l){if(r==null)return{};var s,u,d={},p=Object.keys(r);for(u=0;u<p.length;u++)s=p[u],l.indexOf(s)>=0||(d[s]=r[s]);return d}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ne(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var oe=ne(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),xe=ne(/Edge/i),bt=ne(/firefox/i),Me=ne(/safari/i)&&!ne(/chrome/i)&&!ne(/android/i),yt=ne(/iP(ad|od|hone)/i),wt=ne(/chrome/i)&&ne(/android/i),Et={capture:!1,passive:!1};function b(e,t,n){e.addEventListener(t,n,!oe&&Et)}function m(e,t,n){e.removeEventListener(t,n,!oe&&Et)}function Be(e,t){if(t){if(t[0]===">"&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch{return!1}return!1}}function St(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function V(e,t,n,o){if(e){n=n||document;do{if(t!=null&&(t[0]===">"?e.parentNode===n&&Be(e,t):Be(e,t))||o&&e===n)return e;if(e===n)break}while(e=St(e))}return null}var Oe,_t=/\s+/g;function H(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var o=(" "+e.className+" ").replace(_t," ").replace(" "+t+" "," ");e.className=(o+(n?" "+t:"")).replace(_t," ")}}function h(e,t,n){var o=e&&e.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),t===void 0?n:n[t];t in o||t.indexOf("webkit")!==-1||(t="-webkit-"+t),o[t]=n+(typeof n=="string"?"":"px")}}function me(e,t){var n="";if(typeof e=="string")n=e;else do{var o=h(e,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!t&&(e=e.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(n)}function Dt(e,t,n){if(e){var o=e.getElementsByTagName(t),i=0,a=o.length;if(n)for(;i<a;i++)n(o[i],i);return o}return[]}function $(){var e=document.scrollingElement;return e||document.documentElement}function x(e,t,n,o,i){if(e.getBoundingClientRect||e===window){var a,r,l,s,u,d,p;if(e!==window&&e.parentNode&&e!==$()?(r=(a=e.getBoundingClientRect()).top,l=a.left,s=a.bottom,u=a.right,d=a.height,p=a.width):(r=0,l=0,s=window.innerHeight,u=window.innerWidth,d=window.innerHeight,p=window.innerWidth),(t||n)&&e!==window&&(i=i||e.parentNode,!oe))do if(i&&i.getBoundingClientRect&&(h(i,"transform")!=="none"||n&&h(i,"position")!=="static")){var _=i.getBoundingClientRect();r-=_.top+parseInt(h(i,"border-top-width")),l-=_.left+parseInt(h(i,"border-left-width")),s=r+a.height,u=l+a.width;break}while(i=i.parentNode);if(o&&e!==window){var v=me(i||e),C=v&&v.a,O=v&&v.d;v&&(s=(r/=O)+(d/=O),u=(l/=C)+(p/=C))}return{top:r,left:l,bottom:s,right:u,width:p,height:d}}}function Tt(e,t,n){for(var o=le(e,!0),i=x(e)[t];o;){if(!(i>=x(o)[n]))return o;if(o===$())break;o=le(o,!1)}return!1}function be(e,t,n,o){for(var i=0,a=0,r=e.children;a<r.length;){if(r[a].style.display!=="none"&&r[a]!==f.ghost&&(o||r[a]!==f.dragged)&&V(r[a],n.draggable,e,!1)){if(i===t)return r[a];i++}a++}return null}function tt(e,t){for(var n=e.lastElementChild;n&&(n===f.ghost||h(n,"display")==="none"||t&&!Be(n,t));)n=n.previousElementSibling;return n||null}function z(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()==="TEMPLATE"||e===f.clone||t&&!Be(e,t)||n++;return n}function Ct(e){var t=0,n=0,o=$();if(e)do{var i=me(e),a=i.a,r=i.d;t+=e.scrollLeft*a,n+=e.scrollTop*r}while(e!==o&&(e=e.parentNode));return[t,n]}function le(e,t){if(!e||!e.getBoundingClientRect)return $();var n=e,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=h(n);if(n.clientWidth<n.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return $();if(o||t)return n;o=!0}}while(n=n.parentNode);return $()}function nt(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function xt(e,t){return function(){if(!Oe){var n=arguments;n.length===1?e.call(this,n[0]):e.apply(this,n),Oe=setTimeout(function(){Oe=void 0},t)}}}function Mt(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function Ot(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}function Nt(e,t,n){var o={};return Array.from(e.children).forEach(function(i){var a,r,l,s;if(V(i,t.draggable,e,!1)&&!i.animated&&i!==n){var u=x(i);o.left=Math.min((a=o.left)!==null&&a!==void 0?a:1/0,u.left),o.top=Math.min((r=o.top)!==null&&r!==void 0?r:1/0,u.top),o.right=Math.max((l=o.right)!==null&&l!==void 0?l:-1/0,u.right),o.bottom=Math.max((s=o.bottom)!==null&&s!==void 0?s:-1/0,u.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var Y="Sortable"+new Date().getTime();function zt(){var e,t=[];return{captureAnimationState:function(){t=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(n){if(h(n,"display")!=="none"&&n!==f.ghost){t.push({target:n,rect:x(n)});var o=K({},t[t.length-1].rect);if(n.thisAnimationDuration){var i=me(n,!0);i&&(o.top-=i.f,o.left-=i.e)}n.fromRect=o}})},addAnimationState:function(n){t.push(n)},removeAnimationState:function(n){t.splice(function(o,i){for(var a in o)if(o.hasOwnProperty(a)){for(var r in i)if(i.hasOwnProperty(r)&&i[r]===o[a][r])return Number(a)}return-1}(t,{target:n}),1)},animateAll:function(n){var o=this;if(!this.options.animation)return clearTimeout(e),void(typeof n=="function"&&n());var i=!1,a=0;t.forEach(function(r){var l=0,s=r.target,u=s.fromRect,d=x(s),p=s.prevFromRect,_=s.prevToRect,v=r.rect,C=me(s,!0);C&&(d.top-=C.f,d.left-=C.e),s.toRect=d,s.thisAnimationDuration&&nt(p,d)&&!nt(u,d)&&(v.top-d.top)/(v.left-d.left)==(u.top-d.top)/(u.left-d.left)&&(l=function(O,w,k,Q){return Math.sqrt(Math.pow(w.top-O.top,2)+Math.pow(w.left-O.left,2))/Math.sqrt(Math.pow(w.top-k.top,2)+Math.pow(w.left-k.left,2))*Q.animation}(v,p,_,o.options)),nt(d,u)||(s.prevFromRect=u,s.prevToRect=d,l||(l=o.options.animation),o.animate(s,v,d,l)),l&&(i=!0,a=Math.max(a,l),clearTimeout(s.animationResetTimer),s.animationResetTimer=setTimeout(function(){s.animationTime=0,s.prevFromRect=null,s.fromRect=null,s.prevToRect=null,s.thisAnimationDuration=null},l),s.thisAnimationDuration=l)}),clearTimeout(e),i?e=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),t=[]},animate:function(n,o,i,a){if(a){h(n,"transition",""),h(n,"transform","");var r=me(this.el),l=r&&r.a,s=r&&r.d,u=(o.left-i.left)/(l||1),d=(o.top-i.top)/(s||1);n.animatingX=!!u,n.animatingY=!!d,h(n,"transform","translate3d("+u+"px,"+d+"px,0)"),this.forRepaintDummy=function(p){return p.offsetWidth}(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}var ye=[],ot={initializeByDefault:!0},Ne={mount:function(e){for(var t in ot)ot.hasOwnProperty(t)&&!(t in e)&&(e[t]=ot[t]);ye.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),ye.push(e)},pluginEvent:function(e,t,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var i=e+"Global";ye.forEach(function(a){t[a.pluginName]&&(t[a.pluginName][i]&&t[a.pluginName][i](K({sortable:t},n)),t.options[a.pluginName]&&t[a.pluginName][e]&&t[a.pluginName][e](K({sortable:t},n)))})},initializePlugins:function(e,t,n,o){for(var i in ye.forEach(function(r){var l=r.pluginName;if(e.options[l]||r.initializeByDefault){var s=new r(e,t,e.options);s.sortable=e,s.options=e.options,e[l]=s,te(n,s.defaults)}}),e.options)if(e.options.hasOwnProperty(i)){var a=this.modifyOption(e,i,e.options[i]);a!==void 0&&(e.options[i]=a)}},getEventProperties:function(e,t){var n={};return ye.forEach(function(o){typeof o.eventProperties=="function"&&te(n,o.eventProperties.call(t[o.pluginName],e))}),n},modifyOption:function(e,t,n){var o;return ye.forEach(function(i){e[i.pluginName]&&i.optionListeners&&typeof i.optionListeners[t]=="function"&&(o=i.optionListeners[t].call(e[i.pluginName],n))}),o}},Gt=["evt"],R=function(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=n.evt,i=Wt(n,Gt);Ne.pluginEvent.bind(f)(e,t,K({dragEl:c,parentEl:D,ghostEl:g,rootEl:S,nextEl:he,lastDownEl:Fe,cloneEl:T,cloneHidden:se,dragStarted:Pe,putSortable:A,activeSortable:f.active,originalEvent:o,oldIndex:we,oldDraggableIndex:Ae,newIndex:L,newDraggableIndex:ce,hideGhostForTarget:Rt,unhideGhostForTarget:Bt,cloneNowHidden:function(){se=!0},cloneNowShown:function(){se=!1},dispatchSortableEvent:function(a){I({sortable:t,name:a,originalEvent:o})}},i))};function I(e){(function(t){var n=t.sortable,o=t.rootEl,i=t.name,a=t.targetEl,r=t.cloneEl,l=t.toEl,s=t.fromEl,u=t.oldIndex,d=t.newIndex,p=t.oldDraggableIndex,_=t.newDraggableIndex,v=t.originalEvent,C=t.putSortable,O=t.extraEventProperties;if(n=n||o&&o[Y]){var w,k=n.options,Q="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||oe||xe?(w=document.createEvent("Event")).initEvent(i,!0,!0):w=new CustomEvent(i,{bubbles:!0,cancelable:!0}),w.to=l||o,w.from=s||o,w.item=a||o,w.clone=r,w.oldIndex=u,w.newIndex=d,w.oldDraggableIndex=p,w.newDraggableIndex=_,w.originalEvent=v,w.pullMode=C?C.lastPutMode:void 0;var j=K(K({},O),Ne.getEventProperties(i,n));for(var ie in j)w[ie]=j[ie];o&&o.dispatchEvent(w),k[Q]&&k[Q].call(n,w)}})(K({putSortable:A,cloneEl:T,targetEl:c,rootEl:S,oldIndex:we,oldDraggableIndex:Ae,newIndex:L,newDraggableIndex:ce},e))}var c,D,g,S,he,Fe,T,se,we,L,Ae,ce,He,A,fe,q,it,rt,At,Pt,Pe,Ee,Ie,Le,P,Se=!1,je=!1,We=[],ke=!1,ze=!1,at=[],lt=!1,Ge=[],Ue=typeof document<"u",Ve=yt,It=xe||oe?"cssFloat":"float",Ut=Ue&&!wt&&!yt&&"draggable"in document.createElement("div"),kt=function(){if(Ue){if(oe)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),Xt=function(e,t){var n=h(e),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=be(e,0,t),a=be(e,1,t),r=i&&h(i),l=a&&h(a),s=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+x(i).width,u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+x(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&r.float&&r.float!=="none"){var d=r.float==="left"?"left":"right";return!a||l.clear!=="both"&&l.clear!==d?"horizontal":"vertical"}return i&&(r.display==="block"||r.display==="flex"||r.display==="table"||r.display==="grid"||s>=o&&n[It]==="none"||a&&n[It]==="none"&&s+u>o)?"vertical":"horizontal"},Yt=function(e){function t(i,a){return function(r,l,s,u){var d=r.options.group.name&&l.options.group.name&&r.options.group.name===l.options.group.name;if(i==null&&(a||d))return!0;if(i==null||i===!1)return!1;if(a&&i==="clone")return i;if(typeof i=="function")return t(i(r,l,s,u),a)(r,l,s,u);var p=(a?r:l).options.group.name;return i===!0||typeof i=="string"&&i===p||i.join&&i.indexOf(p)>-1}}var n={},o=e.group;o&&et(o)=="object"||(o={name:o}),n.name=o.name,n.checkPull=t(o.pull,!0),n.checkPut=t(o.put),n.revertClone=o.revertClone,e.group=n},Rt=function(){!kt&&g&&h(g,"display","none")},Bt=function(){!kt&&g&&h(g,"display","")};Ue&&!wt&&document.addEventListener("click",function(e){if(je)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),je=!1,!1},!0);var pe=function(e){if(c){e=e.touches?e.touches[0]:e;var t=(i=e.clientX,a=e.clientY,We.some(function(l){var s=l[Y].options.emptyInsertThreshold;if(s&&!tt(l)){var u=x(l),d=i>=u.left-s&&i<=u.right+s,p=a>=u.top-s&&a<=u.bottom+s;return d&&p?r=l:void 0}}),r);if(t){var n={};for(var o in e)e.hasOwnProperty(o)&&(n[o]=e[o]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[Y]._onDragOver(n)}}var i,a,r},Vt=function(e){c&&c.parentNode[Y]._isOutsideThisEl(e.target)};function f(e,t){if(!e||!e.nodeType||e.nodeType!==1)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=te({},t),e[Y]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Xt(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,r){a.setData("Text",r.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:f.supportPointer!==!1&&"PointerEvent"in window&&!Me,emptyInsertThreshold:5};for(var o in Ne.initializePlugins(this,e,n),n)!(o in t)&&(t[o]=n[o]);for(var i in Yt(t),this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=!t.forceFallback&&Ut,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?b(e,"pointerdown",this._onTapStart):(b(e,"mousedown",this._onTapStart),b(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(b(e,"dragover",this),b(e,"dragenter",this)),We.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),te(this,zt())}function qe(e,t,n,o,i,a,r,l){var s,u,d=e[Y],p=d.options.onMove;return!window.CustomEvent||oe||xe?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=t,s.from=e,s.dragged=n,s.draggedRect=o,s.related=i||t,s.relatedRect=a||x(t),s.willInsertAfter=l,s.originalEvent=r,e.dispatchEvent(s),p&&(u=p.call(d,s,r)),u}function st(e){e.draggable=!1}function qt(){lt=!1}function Qt(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,o=0;n--;)o+=t.charCodeAt(n);return o.toString(36)}function Qe(e){return setTimeout(e,0)}function ct(e){return clearTimeout(e)}f.prototype={constructor:f,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(Ee=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,c):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,o=this.options,i=o.preventOnFilter,a=e.type,r=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,l=(r||e).target,s=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||l,u=o.filter;if(function(d){Ge.length=0;for(var p=d.getElementsByTagName("input"),_=p.length;_--;){var v=p[_];v.checked&&Ge.push(v)}}(n),!c&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||o.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!Me||!l||l.tagName.toUpperCase()!=="SELECT")&&!((l=V(l,o.draggable,n,!1))&&l.animated||Fe===l)){if(we=z(l),Ae=z(l,o.draggable),typeof u=="function"){if(u.call(this,e,l,this))return I({sortable:t,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),R("filter",t,{evt:e}),void(i&&e.cancelable&&e.preventDefault())}else if(u&&(u=u.split(",").some(function(d){if(d=V(s,d.trim(),n,!1))return I({sortable:t,rootEl:d,name:"filter",targetEl:l,fromEl:n,toEl:n}),R("filter",t,{evt:e}),!0})))return void(i&&e.cancelable&&e.preventDefault());o.handle&&!V(s,o.handle,n,!1)||this._prepareDragStart(e,r,l)}}},_prepareDragStart:function(e,t,n){var o,i=this,a=i.el,r=i.options,l=a.ownerDocument;if(n&&!c&&n.parentNode===a){var s=x(n);if(S=a,D=(c=n).parentNode,he=c.nextSibling,Fe=n,He=r.group,f.dragged=c,fe={target:c,clientX:(t||e).clientX,clientY:(t||e).clientY},At=fe.clientX-s.left,Pt=fe.clientY-s.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,c.style["will-change"]="all",o=function(){R("delayEnded",i,{evt:e}),f.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!bt&&i.nativeDraggable&&(c.draggable=!0),i._triggerDragStart(e,t),I({sortable:i,name:"choose",originalEvent:e}),H(c,r.chosenClass,!0))},r.ignore.split(",").forEach(function(u){Dt(c,u.trim(),st)}),b(l,"dragover",pe),b(l,"mousemove",pe),b(l,"touchmove",pe),b(l,"mouseup",i._onDrop),b(l,"touchend",i._onDrop),b(l,"touchcancel",i._onDrop),bt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),R("delayStart",this,{evt:e}),!r.delay||r.delayOnTouchOnly&&!t||this.nativeDraggable&&(xe||oe))o();else{if(f.eventCanceled)return void this._onDrop();b(l,"mouseup",i._disableDelayedDrag),b(l,"touchend",i._disableDelayedDrag),b(l,"touchcancel",i._disableDelayedDrag),b(l,"mousemove",i._delayedDragTouchMoveHandler),b(l,"touchmove",i._delayedDragTouchMoveHandler),r.supportPointer&&b(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,r.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&st(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;m(e,"mouseup",this._disableDelayedDrag),m(e,"touchend",this._disableDelayedDrag),m(e,"touchcancel",this._disableDelayedDrag),m(e,"mousemove",this._delayedDragTouchMoveHandler),m(e,"touchmove",this._delayedDragTouchMoveHandler),m(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?b(document,"pointermove",this._onTouchMove):b(document,t?"touchmove":"mousemove",this._onTouchMove):(b(c,"dragend",this),b(S,"dragstart",this._onDragStart));try{document.selection?Qe(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(Se=!1,S&&c){R("dragStarted",this,{evt:t}),this.nativeDraggable&&b(document,"dragover",Vt);var n=this.options;!e&&H(c,n.dragClass,!1),H(c,n.ghostClass,!0),f.active=this,e&&this._appendGhost(),I({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(q){this._lastX=q.clientX,this._lastY=q.clientY,Rt();for(var e=document.elementFromPoint(q.clientX,q.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(q.clientX,q.clientY))!==t;)t=e;if(c.parentNode[Y]._isOutsideThisEl(e),t)do{if(t[Y]&&t[Y]._onDragOver({clientX:q.clientX,clientY:q.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break;e=t}while(t=St(t));Bt()}},_onTouchMove:function(e){if(fe){var t=this.options,n=t.fallbackTolerance,o=t.fallbackOffset,i=e.touches?e.touches[0]:e,a=g&&me(g,!0),r=g&&a&&a.a,l=g&&a&&a.d,s=Ve&&P&&Ct(P),u=(i.clientX-fe.clientX+o.x)/(r||1)+(s?s[0]-at[0]:0)/(r||1),d=(i.clientY-fe.clientY+o.y)/(l||1)+(s?s[1]-at[1]:0)/(l||1);if(!f.active&&!Se){if(n&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(g){a?(a.e+=u-(it||0),a.f+=d-(rt||0)):a={a:1,b:0,c:0,d:1,e:u,f:d};var p="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(g,"webkitTransform",p),h(g,"mozTransform",p),h(g,"msTransform",p),h(g,"transform",p),it=u,rt=d,q=i}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!g){var e=this.options.fallbackOnBody?document.body:S,t=x(c,!0,Ve,!0,e),n=this.options;if(Ve){for(P=e;h(P,"position")==="static"&&h(P,"transform")==="none"&&P!==document;)P=P.parentNode;P!==document.body&&P!==document.documentElement?(P===document&&(P=$()),t.top+=P.scrollTop,t.left+=P.scrollLeft):P=$(),at=Ct(P)}H(g=c.cloneNode(!0),n.ghostClass,!1),H(g,n.fallbackClass,!0),H(g,n.dragClass,!0),h(g,"transition",""),h(g,"transform",""),h(g,"box-sizing","border-box"),h(g,"margin",0),h(g,"top",t.top),h(g,"left",t.left),h(g,"width",t.width),h(g,"height",t.height),h(g,"opacity","0.8"),h(g,"position",Ve?"absolute":"fixed"),h(g,"zIndex","100000"),h(g,"pointerEvents","none"),f.ghost=g,e.appendChild(g),h(g,"transform-origin",At/parseInt(g.style.width)*100+"% "+Pt/parseInt(g.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,o=e.dataTransfer,i=n.options;R("dragStart",this,{evt:e}),f.eventCanceled?this._onDrop():(R("setupClone",this),f.eventCanceled||((T=Ot(c)).removeAttribute("id"),T.draggable=!1,T.style["will-change"]="",this._hideClone(),H(T,this.options.chosenClass,!1),f.clone=T),n.cloneId=Qe(function(){R("clone",n),f.eventCanceled||(n.options.removeCloneOnHide||S.insertBefore(T,c),n._hideClone(),I({sortable:n,name:"clone"}))}),!t&&H(c,i.dragClass,!0),t?(je=!0,n._loopId=setInterval(n._emulateDragOver,50)):(m(document,"mouseup",n._onDrop),m(document,"touchend",n._onDrop),m(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,c)),b(document,"drop",n),h(c,"transform","translateZ(0)")),Se=!0,n._dragStartId=Qe(n._dragStarted.bind(n,t,e)),b(document,"selectstart",n),Pe=!0,Me&&h(document.body,"user-select","none"))},_onDragOver:function(e){var t,n,o,i,a=this.el,r=e.target,l=this.options,s=l.group,u=f.active,d=He===s,p=l.sort,_=A||u,v=this,C=!1;if(!lt){if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),r=V(r,l.draggable,a,!0),Z("dragOver"),f.eventCanceled)return C;if(c.contains(e.target)||r.animated&&r.animatingX&&r.animatingY||v._ignoreWhileAnimating===r)return F(!1);if(je=!1,u&&!l.disabled&&(d?p||(o=D!==S):A===this||(this.lastPutMode=He.checkPull(this,u,c,e))&&s.checkPut(this,u,c,e))){if(i=this._getDirection(e,r)==="vertical",t=x(c),Z("dragOverValid"),f.eventCanceled)return C;if(o)return D=S,ee(),this._hideClone(),Z("revert"),f.eventCanceled||(he?S.insertBefore(c,he):S.appendChild(c)),F(!0);var O=tt(a,l.draggable);if(!O||function(y,X,E){var N=x(tt(E.el,E.options.draggable)),G=Nt(E.el,E.options,g),W=10;return X?y.clientX>G.right+W||y.clientY>N.bottom&&y.clientX>N.left:y.clientY>G.bottom+W||y.clientX>N.right&&y.clientY>N.top}(e,i,this)&&!O.animated){if(O===c)return F(!1);if(O&&a===e.target&&(r=O),r&&(n=x(r)),qe(S,a,c,t,r,n,e,!!r)!==!1)return ee(),O&&O.nextSibling?a.insertBefore(c,O.nextSibling):a.appendChild(c),D=a,de(),F(!0)}else if(O&&function(y,X,E){var N=x(be(E.el,0,E.options,!0)),G=Nt(E.el,E.options,g),W=10;return X?y.clientX<G.left-W||y.clientY<N.top&&y.clientX<N.right:y.clientY<G.top-W||y.clientY<N.bottom&&y.clientX<N.left}(e,i,this)){var w=be(a,0,l,!0);if(w===c)return F(!1);if(n=x(r=w),qe(S,a,c,t,r,n,e,!1)!==!1)return ee(),a.insertBefore(c,w),D=a,de(),F(!0)}else if(r.parentNode===a){n=x(r);var k,Q,j,ie=c.parentNode!==a,ge=!function(y,X,E){var N=E?y.left:y.top,G=E?y.right:y.bottom,W=E?y.width:y.height,Te=E?X.left:X.top,$e=E?X.right:X.bottom,U=E?X.width:X.height;return N===Te||G===$e||N+W/2===Te+U/2}(c.animated&&c.toRect||t,r.animated&&r.toRect||n,i),ve=i?"top":"left",J=Tt(r,"top","top")||Tt(c,"top","top"),_e=J?J.scrollTop:void 0;if(Ee!==r&&(Q=n[ve],ke=!1,ze=!ge&&l.invertSwap||ie),k=function(y,X,E,N,G,W,Te,$e){var U=N?y.clientY:y.clientX,ae=N?E.height:E.width,Ce=N?E.top:E.left,Re=N?E.bottom:E.right,Je=!1;if(!Te){if($e&&Le<ae*G){if(!ke&&(Ie===1?U>Ce+ae*W/2:U<Re-ae*W/2)&&(ke=!0),ke)Je=!0;else if(Ie===1?U<Ce+Le:U>Re-Le)return-Ie}else if(U>Ce+ae*(1-G)/2&&U<Re-ae*(1-G)/2)return function(Lt){return z(c)<z(Lt)?1:-1}(X)}return(Je=Je||Te)&&(U<Ce+ae*W/2||U>Re-ae*W/2)?U>Ce+ae/2?1:-1:0}(e,r,n,i,ge?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,ze,Ee===r),k!==0){var B=z(c);do B-=k,j=D.children[B];while(j&&(h(j,"display")==="none"||j===g))}if(k===0||j===r)return F(!1);Ee=r,Ie=k;var De=r.nextElementSibling,ue=!1,re=qe(S,a,c,t,r,n,e,ue=k===1);if(re!==!1)return re!==1&&re!==-1||(ue=re===1),lt=!0,setTimeout(qt,30),ee(),ue&&!De?a.appendChild(c):r.parentNode.insertBefore(c,ue?De:r),J&&Mt(J,0,_e-J.scrollTop),D=c.parentNode,Q===void 0||ze||(Le=Math.abs(Q-x(r)[ve])),de(),F(!0)}if(a.contains(c))return F(!1)}return!1}function Z(y,X){R(y,v,K({evt:e,isOwner:d,axis:i?"vertical":"horizontal",revert:o,dragRect:t,targetRect:n,canSort:p,fromSortable:_,target:r,completed:F,onMove:function(E,N){return qe(S,a,c,t,E,x(E),e,N)},changed:de},X))}function ee(){Z("dragOverAnimationCapture"),v.captureAnimationState(),v!==_&&_.captureAnimationState()}function F(y){return Z("dragOverCompleted",{insertion:y}),y&&(d?u._hideClone():u._showClone(v),v!==_&&(H(c,A?A.options.ghostClass:u.options.ghostClass,!1),H(c,l.ghostClass,!0)),A!==v&&v!==f.active?A=v:v===f.active&&A&&(A=null),_===v&&(v._ignoreWhileAnimating=r),v.animateAll(function(){Z("dragOverAnimationComplete"),v._ignoreWhileAnimating=null}),v!==_&&(_.animateAll(),_._ignoreWhileAnimating=null)),(r===c&&!c.animated||r===a&&!r.animated)&&(Ee=null),l.dragoverBubble||e.rootEl||r===document||(c.parentNode[Y]._isOutsideThisEl(e.target),!y&&pe(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),C=!0}function de(){L=z(c),ce=z(c,l.draggable),I({sortable:v,name:"change",toEl:a,newIndex:L,newDraggableIndex:ce,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){m(document,"mousemove",this._onTouchMove),m(document,"touchmove",this._onTouchMove),m(document,"pointermove",this._onTouchMove),m(document,"dragover",pe),m(document,"mousemove",pe),m(document,"touchmove",pe)},_offUpEvents:function(){var e=this.el.ownerDocument;m(e,"mouseup",this._onDrop),m(e,"touchend",this._onDrop),m(e,"pointerup",this._onDrop),m(e,"touchcancel",this._onDrop),m(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;L=z(c),ce=z(c,n.draggable),R("drop",this,{evt:e}),D=c&&c.parentNode,L=z(c),ce=z(c,n.draggable),f.eventCanceled||(Se=!1,ze=!1,ke=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ct(this.cloneId),ct(this._dragStartId),this.nativeDraggable&&(m(document,"drop",this),m(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Me&&h(document.body,"user-select",""),h(c,"transform",""),e&&(Pe&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(S===D||A&&A.lastPutMode!=="clone")&&T&&T.parentNode&&T.parentNode.removeChild(T),c&&(this.nativeDraggable&&m(c,"dragend",this),st(c),c.style["will-change"]="",Pe&&!Se&&H(c,A?A.options.ghostClass:this.options.ghostClass,!1),H(c,this.options.chosenClass,!1),I({sortable:this,name:"unchoose",toEl:D,newIndex:null,newDraggableIndex:null,originalEvent:e}),S!==D?(L>=0&&(I({rootEl:D,name:"add",toEl:D,fromEl:S,originalEvent:e}),I({sortable:this,name:"remove",toEl:D,originalEvent:e}),I({rootEl:D,name:"sort",toEl:D,fromEl:S,originalEvent:e}),I({sortable:this,name:"sort",toEl:D,originalEvent:e})),A&&A.save()):L!==we&&L>=0&&(I({sortable:this,name:"update",toEl:D,originalEvent:e}),I({sortable:this,name:"sort",toEl:D,originalEvent:e})),f.active&&(L!=null&&L!==-1||(L=we,ce=Ae),I({sortable:this,name:"end",toEl:D,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){R("nulling",this),S=c=D=g=he=T=Fe=se=fe=q=Pe=L=ce=we=Ae=Ee=Ie=A=He=f.dragged=f.ghost=f.clone=f.active=null,Ge.forEach(function(e){e.checked=!0}),Ge.length=it=rt=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":c&&(this._onDragOver(e),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],n=this.el.children,o=0,i=n.length,a=this.options;o<i;o++)V(e=n[o],a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||Qt(e));return t},sort:function(e,t){var n={},o=this.el;this.toArray().forEach(function(i,a){var r=o.children[a];V(r,this.options.draggable,o,!1)&&(n[i]=r)},this),t&&this.captureAnimationState(),e.forEach(function(i){n[i]&&(o.removeChild(n[i]),o.appendChild(n[i]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return V(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(t===void 0)return n[e];var o=Ne.modifyOption(this,e,t);n[e]=o!==void 0?o:t,e==="group"&&Yt(n)},destroy:function(){R("destroy",this);var e=this.el;e[Y]=null,m(e,"mousedown",this._onTapStart),m(e,"touchstart",this._onTapStart),m(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(m(e,"dragover",this),m(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),We.splice(We.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!se){if(R("hideClone",this),f.eventCanceled)return;h(T,"display","none"),this.options.removeCloneOnHide&&T.parentNode&&T.parentNode.removeChild(T),se=!0}},_showClone:function(e){if(e.lastPutMode==="clone"){if(se){if(R("showClone",this),f.eventCanceled)return;c.parentNode!=S||this.options.group.revertClone?he?S.insertBefore(T,he):S.appendChild(T):S.insertBefore(T,c),this.options.group.revertClone&&this.animate(c,T),h(T,"display",""),se=!1}}else this._hideClone()}},Ue&&b(document,"touchmove",function(e){(f.active||Se)&&e.cancelable&&e.preventDefault()}),f.utils={on:b,off:m,css:h,find:Dt,is:function(e,t){return!!V(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},throttle:xt,closest:V,toggleClass:H,clone:Ot,index:z,nextTick:Qe,cancelNextTick:ct,detectDirection:Xt,getChild:be,expando:Y},f.get=function(e){return e[Y]},f.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(f.utils=K(K({},f.utils),o.utils)),Ne.mount(o)})},f.create=function(e,t){return new f(e,t)},f.version="1.15.3";var Xe,ut,dt,ht,Ze,Ye,M=[],ft=!1;function Ke(){M.forEach(function(e){clearInterval(e.pid)}),M=[]}function Ft(){clearInterval(Ye)}var pt=xt(function(e,t,n,o){if(t.scroll){var i,a=(e.touches?e.touches[0]:e).clientX,r=(e.touches?e.touches[0]:e).clientY,l=t.scrollSensitivity,s=t.scrollSpeed,u=$(),d=!1;ut!==n&&(ut=n,Ke(),Xe=t.scroll,i=t.scrollFn,Xe===!0&&(Xe=le(n,!0)));var p=0,_=Xe;do{var v=_,C=x(v),O=C.top,w=C.bottom,k=C.left,Q=C.right,j=C.width,ie=C.height,ge=void 0,ve=void 0,J=v.scrollWidth,_e=v.scrollHeight,B=h(v),De=v.scrollLeft,ue=v.scrollTop;v===u?(ge=j<J&&(B.overflowX==="auto"||B.overflowX==="scroll"||B.overflowX==="visible"),ve=ie<_e&&(B.overflowY==="auto"||B.overflowY==="scroll"||B.overflowY==="visible")):(ge=j<J&&(B.overflowX==="auto"||B.overflowX==="scroll"),ve=ie<_e&&(B.overflowY==="auto"||B.overflowY==="scroll"));var re=ge&&(Math.abs(Q-a)<=l&&De+j<J)-(Math.abs(k-a)<=l&&!!De),Z=ve&&(Math.abs(w-r)<=l&&ue+ie<_e)-(Math.abs(O-r)<=l&&!!ue);if(!M[p])for(var ee=0;ee<=p;ee++)M[ee]||(M[ee]={});M[p].vx==re&&M[p].vy==Z&&M[p].el===v||(M[p].el=v,M[p].vx=re,M[p].vy=Z,clearInterval(M[p].pid),re==0&&Z==0||(d=!0,M[p].pid=setInterval((function(){o&&this.layer===0&&f.active._onTouchMove(Ze);var F=M[this.layer].vy?M[this.layer].vy*s:0,de=M[this.layer].vx?M[this.layer].vx*s:0;typeof i=="function"&&i.call(f.dragged.parentNode[Y],de,F,e,Ze,M[this.layer].el)!=="continue"||Mt(M[this.layer].el,de,F)}).bind({layer:p}),24))),p++}while(t.bubbleScroll&&_!==u&&(_=le(_,!1)));ft=d}},30),Ht=function(e){var t=e.originalEvent,n=e.putSortable,o=e.dragEl,i=e.activeSortable,a=e.dispatchSortableEvent,r=e.hideGhostForTarget,l=e.unhideGhostForTarget;if(t){var s=n||i;r();var u=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,d=document.elementFromPoint(u.clientX,u.clientY);l(),s&&!s.el.contains(d)&&(a("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function gt(){}function vt(){}gt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=be(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(t,o):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:Ht},te(gt,{pluginName:"revertOnSpill"}),vt.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable||this.sortable;n.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),n.animateAll()},drop:Ht},te(vt,{pluginName:"removeOnSpill"}),f.mount(new function(){function e(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return e.prototype={dragStarted:function(t){var n=t.originalEvent;this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):this.options.supportPointer?b(document,"pointermove",this._handleFallbackAutoScroll):n.touches?b(document,"touchmove",this._handleFallbackAutoScroll):b(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var n=t.originalEvent;this.options.dragOverBubble||n.rootEl||this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?m(document,"dragover",this._handleAutoScroll):(m(document,"pointermove",this._handleFallbackAutoScroll),m(document,"touchmove",this._handleFallbackAutoScroll),m(document,"mousemove",this._handleFallbackAutoScroll)),Ft(),Ke(),clearTimeout(Oe),Oe=void 0},nulling:function(){Ze=ut=Xe=ft=Ye=dt=ht=null,M.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,n){var o=this,i=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,r=document.elementFromPoint(i,a);if(Ze=t,n||this.options.forceAutoScrollFallback||xe||oe||Me){pt(t,this.options,r,n);var l=le(r,!0);!ft||Ye&&i===dt&&a===ht||(Ye&&Ft(),Ye=setInterval(function(){var s=le(document.elementFromPoint(i,a),!0);s!==l&&(l=s,Ke()),pt(t,o.options,s,n)},10),dt=i,ht=a)}else{if(!this.options.bubbleScroll||le(r,!0)===$())return void Ke();pt(t,this.options,le(r,!1),!1)}}},te(e,{pluginName:"scroll",initializeByDefault:!0})}),f.mount(vt,gt);export{f as S};
