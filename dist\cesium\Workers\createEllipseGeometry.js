define(["./Matrix3-a348023f","./defaultValue-0a909f67","./EllipseGeometry-29874fdd","./Math-e97915da","./Transforms-01e95659","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./ComponentDatatype-77274976","./WebGLConstants-a8cc3e8c","./EllipseGeometryLibrary-e689e77b","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryInstance-99908f4f","./GeometryOffsetAttribute-04332ce7","./GeometryPipeline-049a5b67","./AttributeCompression-50c9aeba","./EncodedCartesian3-0fb84db0","./IndexDatatype-2149f06c","./IntersectionTests-0bb04fde","./Plane-8575e17c","./VertexFormat-ab2e00e6"],(function(e,t,r,a,n,i,o,c,s,l,f,d,m,b,p,u,y,G,E,C,x,A){"use strict";return function(a,n){return t.defined(n)&&(a=r.EllipseGeometry.unpack(a,n)),a._center=e.Cartesian3.clone(a._center),a._ellipsoid=e.Ellipsoid.clone(a._ellipsoid),r.EllipseGeometry.createGeometry(a)}}));
