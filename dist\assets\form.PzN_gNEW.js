import{v as R,c as V,r as j,__tla as S}from"./index.BSP3cg_z.js";import{p as A,a as H,g as O,__tla as P}from"./group.ZBPXjmD0.js";import{l as z,__tla as J}from"./template.BlMVjGfJ.js";import{d as x,k as m,A as K,B as s,m as M,e as _,b as c,v as r,q as Q,u as a,t as u,a as W,F as X,p as Y,f as Z,E as D,G as I,H as ee,y as ae}from"./vue.CnN__PXn.js";let N,le=Promise.all([(()=>{try{return S}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return J}catch{}})()]).then(async()=>{let h,b;h={class:"dialog-footer"},b=x({name:"GenGroupDialog"}),N=x({...b,emits:["refresh"],setup(te,{expose:T,emit:w}){const $=w,{t:p}=R.useI18n(),g=m(),d=m(!1),i=m(!1),v=m([]),e=K({id:"",groupName:"",groupDesc:"",templateId:[],putList:[]}),k=m({groupName:[{validator:j.overLength,trigger:"blur"},{required:!0,message:"\u5206\u7EC4\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],templateId:[{required:!0,message:"\u6A21\u677F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),B=async()=>{if(!await g.value.validate().catch(()=>{}))return!1;try{i.value=!0,e.id?await A(e):await H(e),V().success(p(e.id?"common.editSuccessText":"common.addSuccessText")),d.value=!1,$("refresh")}catch(l){V().error(l.msg)}finally{i.value=!1}},G=l=>{O(l).then(t=>{Object.assign(e,t.data),e.templateId=[],t.data.templateList&&t.data.templateList.forEach(n=>{e.templateId.push(n.id)})})},L=()=>{z().then(l=>{v.value=l.data})};return T({openDialog:l=>{d.value=!0,e.id="",ae(()=>{g.value.resetFields()}),l&&(e.id=l,G(l)),L()}}),(l,t)=>{const n=s("el-input"),f=s("el-form-item"),U=s("el-option"),q=s("el-select"),F=s("el-form"),y=s("el-button"),C=s("el-dialog"),E=M("loading");return c(),_(C,{modelValue:a(d),"onUpdate:modelValue":t[4]||(t[4]=o=>ee(d)?d.value=o:null),title:a(e).id?l.$t("common.editBtn"):l.$t("common.addBtn"),width:"600"},{footer:r(()=>[Z("span",h,[u(y,{onClick:t[3]||(t[3]=o=>d.value=!1)},{default:r(()=>[D(I(l.$t("common.cancelButtonText")),1)]),_:1}),u(y,{type:"primary",onClick:B,disabled:a(i)},{default:r(()=>[D(I(l.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:r(()=>[Q((c(),_(F,{ref_key:"dataFormRef",ref:g,model:a(e),rules:a(k),formDialogRef:"","label-width":"90px"},{default:r(()=>[u(f,{label:a(p)("group.groupName"),prop:"groupName"},{default:r(()=>[u(n,{modelValue:a(e).groupName,"onUpdate:modelValue":t[0]||(t[0]=o=>a(e).groupName=o),placeholder:a(p)("group.inputGroupNameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),u(f,{label:l.$t("group.templateType"),prop:"templateId"},{default:r(()=>[u(q,{modelValue:a(e).templateId,"onUpdate:modelValue":t[1]||(t[1]=o=>a(e).templateId=o),placeholder:l.$t("group.selectType"),clearable:"",multiple:""},{default:r(()=>[(c(!0),W(X,null,Y(a(v),o=>(c(),_(U,{key:o.id,label:o.templateName,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),u(f,{label:a(p)("group.groupDesc"),prop:"groupDesc"},{default:r(()=>[u(n,{type:"textarea",maxlength:"100",rows:5,modelValue:a(e).groupDesc,"onUpdate:modelValue":t[2]||(t[2]=o=>a(e).groupDesc=o),placeholder:a(p)("group.inputGroupDescTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[E,a(i)]])]),_:1},8,["modelValue","title"])}}})});export{le as __tla,N as default};
