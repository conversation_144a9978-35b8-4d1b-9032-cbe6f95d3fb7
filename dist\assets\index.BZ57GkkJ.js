import{S as m,o as R,z as _,__tla as y}from"./index.BSP3cg_z.js";import{d as g,A as h,k as a,c as b,o as v,i as x,a as $,b as M}from"./vue.CnN__PXn.js";let u,N=Promise.all([(()=>{try{return y}catch{}})()]).then(async()=>{let c;c=g({name:"global-websocket"}),u=g({...c,props:{uri:{type:String}},emits:["rollback"],setup(p,{emit:T}){const k=T,w=p,e=h({webSocket:a(),lockReconnect:!1,maxReconnect:6,reconnectTime:0,heartbeat:{interval:3e4,timeout:1e4,pingTimeoutObj:a(),pongTimeoutObj:a(),pingMessage:JSON.stringify({type:"ping"})}}),i=b(()=>m.getToken()),S=b(()=>m.getTenant());v(()=>{s()}),x(()=>{e.webSocket.close(),r(e.heartbeat)});const s=()=>{let t=`ws://${window.location.host}/api${R.adaptationUrl(w.uri)}?access_token=${i.value}&TENANT-ID=${S.value}`;e.webSocket=new WebSocket(t),e.webSocket.onopen=d,e.webSocket.onerror=O,e.webSocket.onmessage=f,e.webSocket.onclose=j},n=()=>{i&&(e.lockReconnect||e.maxReconnect!==-1&&e.reconnectTime>e.maxReconnect||(e.lockReconnect=!0,setTimeout(()=>{e.reconnectTime++,s(),e.lockReconnect=!1},5e3)))},r=t=>{t.pingTimeoutObj&&clearTimeout(t.pingTimeoutObj),t.pongTimeoutObj&&clearTimeout(t.pongTimeoutObj)},l=()=>{const t=e.webSocket,o=e.heartbeat;r(o),o.pingTimeoutObj=setTimeout(()=>{t.readyState===1?(t.send(o.pingMessage),o.pongTimeoutObj=setTimeout(()=>{t.close()},o.timeout)):n()},o.interval)},d=()=>{l(),e.reconnectTime=0},O=()=>{n()},j=()=>{n()},f=t=>{l();const o=t.data;o.indexOf("pong")>0||(_.warning({title:"\u6D88\u606F\u63D0\u9192",dangerouslyUseHTMLString:!0,message:o+"\u8BF7\u53CA\u65F6\u5904\u7406",offset:60}),k("rollback",o))};return(t,o)=>(M(),$("div"))}})});export{N as __tla,u as default};
