const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.BQuhesfM.js","assets/vue.CnN__PXn.js","assets/index.BSP3cg_z.js","assets/index.DjeikzAe.css","assets/index.BnAUR8RY.css"])))=>i.map(i=>d[i]);
import{a as x,__tla as v}from"./index.BSP3cg_z.js";import{d as g,k as A,A as U,B as _,e as n,b,v as t,t as s,D as y,E as d,G as r,u as e,j as k,H as B}from"./vue.CnN__PXn.js";let f,D=Promise.all([(()=>{try{return v}catch{}})()]).then(async()=>{let c;c=g({name:"log-detail"}),f=g({...c,setup(E,{expose:h}){const p=k(()=>x(()=>import("./index.BQuhesfM.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4]))),i=A(!1),l=U({});return h({openDialog:a=>{i.value=!0,Object.assign(l,a)}}),(a,u)=>{const o=_("el-descriptions-item"),$=_("el-descriptions"),V=_("el-drawer");return b(),n(V,{modelValue:e(i),"onUpdate:modelValue":u[2]||(u[2]=m=>B(i)?i.value=m:null),title:"\u65E5\u5FD7\u8BE6\u60C5",size:"50%"},{default:t(()=>[s($,{column:1},{default:t(()=>[s(o,{label:a.$t("syslog.createTime")},{default:t(()=>[d(r(e(l).createTime),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.createBy")},{default:t(()=>[d(r(e(l).createBy),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.requestUri")},{default:t(()=>[d(r(e(l).requestUri),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.title")},{default:t(()=>[d(r(e(l).title),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.remoteAddr")},{default:t(()=>[d(r(e(l).remoteAddr),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.method")},{default:t(()=>[d(r(e(l).method),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.ua")},{default:t(()=>[d(r(e(l).userAgent),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.serviceId")},{default:t(()=>[d(r(e(l).serviceId),1)]),_:1},8,["label"]),s(o,{label:a.$t("syslog.time")},{default:t(()=>[d(r(e(l).time)+"/ms",1)]),_:1},8,["label"]),e(l).params?(b(),n(o,{key:0,label:a.$t("syslog.params")},{default:t(()=>[s(e(p),{modelValue:e(l).params,"onUpdate:modelValue":u[0]||(u[0]=m=>e(l).params=m),theme:"darcula",height:"100",mode:"go"},null,8,["modelValue"])]),_:1},8,["label"])):y("",!0),e(l).exception?(b(),n(o,{key:1,label:a.$t("syslog.exception")},{default:t(()=>[s(e(p),{modelValue:e(l).exception,"onUpdate:modelValue":u[1]||(u[1]=m=>e(l).exception=m),theme:"darcula",height:"200",mode:"go"},null,8,["modelValue"])]),_:1},8,["label"])):y("",!0)]),_:1})]),_:1},8,["modelValue"])}}})});export{D as __tla,f as default};
