import{d as o,k as I,l as k,c as u,w as p,m as v,a as l,b as n,f as w,F as O,p as b,u as h,q as y,t as _,v as x,x as K,T as P,y as S}from"./vue.CnN__PXn.js";const T={class:"layout-padding layout-padding-unset layout-iframe"},q={class:"layout-padding-auto layout-padding-view"},A=["src","data-url"],E=o({name:"layoutIframeView"}),F=o({...E,props:{refreshKey:{type:String,default:()=>""},name:{type:String,default:()=>"slide-right"},list:{type:Array,default:()=>[]}},setup(d){const i=d,m=I(),r=k(),g=u(()=>i.list.filter(a=>{var e;return(e=a.meta)==null?void 0:e.isIframeOpen})),c=u(()=>r.path),f=(a,e)=>{S(()=>{if(!m.value)return!1;m.value.forEach(s=>{s.dataset.url===a&&(s.onload=()=>{var t;(t=e.meta)!=null&&t.isIframeOpen&&e.meta.loading&&(e.meta.loading=!1)})})})};return p(()=>r.fullPath,a=>{const e=i.list.find(s=>s.path===a);if(!e)return!1;e.meta.isIframeOpen||(e.meta.isIframeOpen=!0),f(a,e)},{immediate:!0}),p(()=>i.refreshKey,()=>{const a=i.list.find(e=>e.path===r.path);if(!a)return!1;a.meta.isIframeOpen&&(a.meta.isIframeOpen=!1),setTimeout(()=>{a.meta.isIframeOpen=!0,a.meta.loading=!0,f(r.fullPath,a)})},{deep:!0}),(a,e)=>{const s=v("loading");return n(),l("div",T,[w("div",q,[(n(!0),l(O,null,b(h(g),t=>y((n(),l("div",{class:"w100",key:t.path,"element-loading-background":"white"},[_(P,{name:d.name},{default:x(()=>[y((n(),l("iframe",{src:t.meta.isLink,key:t.path,frameborder:"0",height:"100%",width:"100%",style:{position:"absolute"},"data-url":t.path,ref_for:!0,ref_key:"iframeRef",ref:m},null,8,A)),[[K,h(c)===t.path]])]),_:2},1032,["name"])])),[[s,t.meta.loading]])),128))])])}}});export{F as default};
