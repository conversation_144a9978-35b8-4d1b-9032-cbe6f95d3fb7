import{f as P,e as q,__tla as B}from"./role.D_YVcts2.js";import{v as G,p as H,o as M,c as N,__tla as U}from"./index.BSP3cg_z.js";import{d as D,k as n,A as z,B as r,m as F,a as J,b as L,t as o,v as c,q as O,u as l,f as u,E as b,G as x}from"./vue.CnN__PXn.js";let w,Q=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{let f,m,p,y,k;f={class:"system-role-dialog-container"},m={class:"flex items-center justify-between"},p={class:"flex mr-16"},y={class:"dialog-footer"},k=D({name:"role-permession"}),w=D({...k,setup(R,{expose:K}){const{t:C}=G.useI18n(),d=n(),I=n(!0),i=n(!1),e=z({checkedKeys:[],treeData:[],defaultProps:{label:"name",value:"id"},roleId:"",dialog:{isShowDialog:!1,title:"\u5206\u914D\u6743\u9650",submitTxt:"\u66F4\u65B0"}}),h=n([]),S=t=>{const a=e.treeData;for(let s=0;s<a.length;s++)d.value.store.nodesMap[a[s].id].expanded=t},T=t=>{var a,s;t?(a=d.value)==null||a.setCheckedKeys(e.treeData.map(g=>g.id)):(s=d.value)==null||s.setCheckedKeys([])},j=()=>{const t=d.value.getCheckedKeys().join(",").concat(",").concat(d.value.getHalfCheckedKeys().join(","));i.value=!0,q(e.roleId,t).then(()=>{e.dialog.isShowDialog=!1,N().success(C("common.editSuccessText"))}).finally(()=>{i.value=!1})};return K({openDialog:t=>{e.checkedKeys=[],e.treeData=[],h.value=[],e.roleId=t.roleId,i.value=!0,P(t.roleId).then(a=>(h.value=a.data,H())).then(a=>{e.treeData=a.data,e.checkedKeys=M.resolveAllEunuchNodeId(e.treeData,h.value,[])}).finally(()=>{i.value=!1}),e.dialog.isShowDialog=!0}}),(t,a)=>{const s=r("el-checkbox"),g=r("el-tree"),V=r("el-scrollbar"),v=r("el-button"),A=r("el-dialog"),E=F("loading");return L(),J("div",f,[o(A,{width:"30%",modelValue:l(e).dialog.isShowDialog,"onUpdate:modelValue":a[1]||(a[1]=_=>l(e).dialog.isShowDialog=_),"close-on-click-modal":!1,draggable:""},{header:c(()=>[u("div",m,[u("div",null,x(l(e).dialog.title),1),u("div",p,[o(s,{label:"\u5C55\u5F00/\u6298\u53E0",onChange:S}),o(s,{label:"\u5168\u9009/\u4E0D\u5168\u9009",onChange:T})])])]),footer:c(()=>[u("span",y,[o(v,{onClick:a[0]||(a[0]=_=>l(e).dialog.isShowDialog=!1)},{default:c(()=>a[2]||(a[2]=[b("\u53D6 \u6D88")])),_:1}),o(v,{type:"primary",onClick:j},{default:c(()=>[b(x(l(e).dialog.submitTxt),1)]),_:1})])]),default:c(()=>[o(V,{class:"h-[400px] sm:h-[600px]"},{default:c(()=>[O(o(g,{ref_key:"menuTree",ref:d,data:l(e).treeData,"default-checked-keys":l(e).checkedKeys,"check-strictly":!l(I),props:l(e).defaultProps,class:"filter-tree","node-key":"id","highlight-current":"","show-checkbox":""},null,8,["data","default-checked-keys","check-strictly","props"]),[[E,l(i)]])]),_:1})]),_:1},8,["modelValue"])])}}})});export{Q as __tla,w as default};
