/**
 * @file olMap.ts
 * @description OpenLayers地图管理类，提供地图初始化、图层管理、视图控制等功能
 * 
 * 该类提供了以下功能：
 * 1. 地图初始化与配置
 * 2. 多种图层（XYZ、WMS、GeoJSON等）的添加与管理
 * 3. 地图视图控制（平移、缩放、飞行等）
 * 4. 图层可见性和透明度控制
 * 5. 基础交互控件（比例尺、全屏、缩放滑块等）
 * 
 * 该类作为原Cesium Earth类的OpenLayers替代实现，保持了相似的API结构
 * 
 * <AUTHOR> Team
 * @date 2025-05-19
 */

import 'ol/ol.css';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import XYZ from 'ol/source/XYZ';
import OSM from 'ol/source/OSM';
import TileWMS from 'ol/source/TileWMS';
import GeoJSON from 'ol/format/GeoJSON';
import { Style, Fill, Stroke, Icon, Circle } from 'ol/style';
import { fromLonLat } from 'ol/proj';
import { defaults as defaultControls, ScaleLine, FullScreen, ZoomSlider } from 'ol/control';
import { defaults as defaultInteractions } from 'ol/interaction';

/**
 * OpenLayers地图类，替代原有的Cesium Earth类
 * 提供地图初始化、图层管理、视图控制等功能
 */
class OLMap {
    /** OpenLayers地图实例 */
    map: Map | null = null;
    /** 图层集合，以ID为键 */
    layers: Record<string, any> = {};
    /** 数据源集合，以ID为键 */
    sources: Record<string, any> = {};
    /** 要素数组 */
    features: any[] = [];

    /**
     * 构造函数
     * @param target 地图容器元素ID或DOM元素
     */
    constructor(target: string | HTMLElement) {
        this.initMap(target);
    }

    /**
     * 初始化地图
     * 创建基础图层、矢量图层，并配置地图控件和交互
     * 
     * @param target 地图容器元素ID或DOM元素
     */
    initMap(target: string | HTMLElement) {
        // 天地图密钥
        const tdtKey = '2e838c79a40d5d7181e153612190bff9';
        
        // 创建天地图矢量底图
        const vecLayer = new TileLayer({
            source: new XYZ({
                url: `https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tdtKey}`,
                crossOrigin: 'anonymous'
            }),
            visible: true,
            zIndex: 0
        });
        
        // 创建天地图矢量注记图层
        const cvaLayer = new TileLayer({
            source: new XYZ({
                url: `https://t{0-7}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tdtKey}`,
                crossOrigin: 'anonymous'
            }),
            visible: true,
            zIndex: 1
        });

        // 保存图层和数据源以便后续使用
        this.layers['tianditu_vec'] = vecLayer;
        this.layers['tianditu_cva'] = cvaLayer;

        // 创建矢量图层 - 用于添加自定义要素
        const vectorSource = new VectorSource();
        const vectorLayer = new VectorLayer({
            source: vectorSource,
            style: new Style({
                fill: new Fill({
                    color: 'rgba(255, 255, 255, 0.2)'
                }),
                stroke: new Stroke({
                    color: '#3388ff',
                    width: 2
                }),
                image: new Circle({
                    radius: 7,
                    fill: new Fill({
                        color: '#3388ff'
                    })
                })
            }),
            zIndex: 2
        });

        // 保存图层和数据源以便后续使用
        this.layers['vector'] = vectorLayer;
        this.sources['vector'] = vectorSource;

        // 创建地图实例
        this.map = new Map({
            target: target,
            // layers: [vecLayer, cvaLayer, vectorLayer],
            layers: [],
            view: new View({
                center: fromLonLat([111.54248000000007, 23.912131999999996]), // 默认中心点
                zoom: 12,
                maxZoom: 20,
                minZoom: 3
            }),
            controls: defaultControls({
                zoom: true,
                rotate: false,
                attribution: false
            }).extend([
                new ScaleLine({
                    units: 'metric',
                    bar: true,
                    steps: 4,
                    text: true,
                    minWidth: 140
                }),
                new FullScreen(),
                new ZoomSlider()
            ]),
            interactions: defaultInteractions({
                pinchRotate: false // 禁用双指旋转
            })
        });

        // 初始化地图配置
        this.initMapConfig();
    }

    /**
     * 获取地图实例
     * @returns OpenLayers Map实例
     */
    getMap() {
        return this.map;
    }

    /**
     * 获取要素列表
     * @returns 要素数组
     */
    getFeatures() {
        return this.features;
    }

    /**
     * 初始化地图配置
     * 可在此方法中添加任何额外的地图初始化配置
     */
    initMapConfig() {
        // 这里可以添加任何额外的地图配置
        console.log('OpenLayers 地图初始化完成');
    }

    /**
     * 飞行到指定位置
     * 以动画方式将地图视图移动到指定的经纬度位置
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param zoom 缩放级别
     * @param duration 动画时长（毫秒）
     */
    flyTo(longitude: number, latitude: number, zoom: number = 12, duration: number = 1000) {
        if (!this.map) return;
        
        const view = this.map.getView();
        view.animate({
            center: fromLonLat([longitude, latitude]),
            zoom: zoom,
            duration: duration
        });
    }

    /**
     * 添加GeoJSON数据
     * 从URL加载GeoJSON数据并创建矢量图层
     * 
     * @param id 图层ID
     * @param url GeoJSON URL
     * @param style 样式函数或样式对象
     * @returns 创建的图层
     */
    async addGeoJSON(id: string, url: string, style?: any) {
        if (!this.map) return null;
        
        try {
            // 获取GeoJSON数据
            const response = await fetch(url);
            const data = await response.json();
            
            // 创建矢量源
            const source = new VectorSource({
                features: new GeoJSON().readFeatures(data, {
                    featureProjection: 'EPSG:4326'
                })
            });
            
            // 创建矢量图层
            const layer = new VectorLayer({
                source: source,
                style: style || this.layers['vector'].getStyle(),
                zIndex: 2
            });
            
            this.map.addLayer(layer);
            this.layers[id] = layer;
            this.sources[id] = source;
            
            return layer;
        } catch (error) {
            console.error('加载GeoJSON失败:', error);
            return null;
        }
    }

    /**
     * 添加WMS图层
     * 创建WMS服务图层并添加到地图
     * 
     * @param id 图层ID
     * @param url WMS服务URL
     * @param layers 图层名称
     * @param options 其他WMS参数
     * @returns 创建的图层
     */
    addWMSLayer(id: string, url: string, layers: string, options: any = {}) {
        if (!this.map) return null;
        
        const source = new TileWMS({
            url: url,
            params: {
                'LAYERS': layers,
                'FORMAT': options.format || 'image/png',
                'TRANSPARENT': options.transparent !== false,
                ...options
            },
            serverType: 'geoserver'
        });
        
        const layer = new TileLayer({
            source: source,
            visible: true,
            zIndex: options.zIndex || 1
        });
        
        this.map.addLayer(layer);
        this.layers[id] = layer;
        this.sources[id] = source;
        
        return layer;
    }

    /**
     * 添加XYZ瓦片图层
     * 创建XYZ瓦片图层并添加到地图
     * 
     * @param id 图层ID
     * @param url 图层URL模板
     * @param options 附加选项
     * @returns 创建的图层
     */
    addXYZLayer(id: string, url: string, options: any = {}) {
        if (!this.map) return null;
        
        const source = new XYZ({
            url: url,
            ...options
        });
        
        const layer = new TileLayer({
            source: source,
            visible: true,
            zIndex: options.zIndex || 0
        });
        
        this.map.addLayer(layer);
        this.layers[id] = layer;
        this.sources[id] = source;
        
        return layer;
    }

    /**
     * 设置图层可见性
     * 控制指定ID的图层是否可见
     * 
     * @param id 图层ID
     * @param visible 是否可见
     */
    setLayerVisibility(id: string, visible: boolean) {
        const layer = this.layers[id];
        if (layer) {
            layer.setVisible(visible);
        }
    }

    /**
     * 设置图层透明度
     * 调整指定ID的图层透明度
     * 
     * @param id 图层ID
     * @param opacity 透明度值（0-1）
     */
    setLayerOpacity(id: string, opacity: number) {
        const layer = this.layers[id];
        if (layer) {
            layer.setOpacity(Math.max(0, Math.min(1, opacity)));
        }
    }

    /**
     * 移除图层
     * 从地图中移除指定ID的图层
     * 
     * @param id 图层ID
     */
    removeLayer(id: string) {
        const layer = this.layers[id];
        if (layer && this.map) {
            this.map.removeLayer(layer);
            delete this.layers[id];
            delete this.sources[id];
        }
    }

    /**
     * 清除所有图层
     * 移除除基础图层外的所有图层
     */
    clearLayers() {
        if (!this.map) return;
        
        // 保留基础图层
        const baseLayer = this.layers['tianditu_vec'];
        const vectorLayer = this.layers['vector'];
        
        // 移除所有其他图层
        Object.keys(this.layers).forEach(id => {
            if (id !== 'tianditu_vec' && id !== 'vector') {
                this.map!.removeLayer(this.layers[id]);
                delete this.layers[id];
                delete this.sources[id];
            }
        });
        
        // 清空矢量图层的要素
        if (this.sources['vector']) {
            this.sources['vector'].clear();
        }
        
        this.features = [];
    }
}

export default OLMap; 