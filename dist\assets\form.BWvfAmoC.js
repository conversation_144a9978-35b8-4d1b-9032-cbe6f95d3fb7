import{v as j,c as f,r as A,__tla as P}from"./index.BSP3cg_z.js";import{d as E,__tla as G}from"./dept.B9Hc3gR-.js";import{p as H,a as O,g as z,v as J,b as K,__tla as L}from"./role.D_YVcts2.js";import{d as y,k as m,A as v,B as u,m as M,e as D,b as x,v as t,q as Q,u as r,t as s,f as W,E as V,G as C,H as X,y as Y}from"./vue.CnN__PXn.js";let I,Z=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let g,b;g={class:"dialog-footer"},b=y({name:"systemRoleDialog"}),I=y({...b,emits:["refresh"],setup(ee,{expose:$,emit:k}){const S=k,{t:w}=j.useI18n(),n=m();m();const c=m(!1),i=m(!1),l=v({roleId:"",roleName:"",roleCode:"",roleDesc:"",dsScope:""}),p=v({deptData:[],checkedDsScope:[],deptProps:{children:"children",label:"name",value:"id"}}),N=m({roleName:[{required:!0,message:"\u89D2\u8272\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{min:3,max:20,message:"\u957F\u5EA6\u5728 3 \u5230 20 \u4E2A\u5B57\u7B26",trigger:"blur"},{validator:(e,a,d)=>{K(e,a,d,l.roleId!=="")},trigger:"blur"}],roleCode:[{required:!0,message:"\u89D2\u8272\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{min:3,max:20,message:"\u957F\u5EA6\u5728 3 \u5230 20 \u4E2A\u5B57\u7B26",trigger:"blur"},{validator:A.validatorCapital,trigger:"blur"},{validator:(e,a,d)=>{J(e,a,d,l.roleId!=="")},trigger:"blur"}],roleDesc:[{max:128,message:"\u957F\u5EA6\u5728 128 \u4E2A\u5B57\u7B26\u5185",trigger:"blur"}]}),B=async()=>{if(!await n.value.validate().catch(()=>{}))return!1;try{i.value=!0,l.roleId?await H(l):await O(l),f().success(w(l.roleId?"common.editSuccessText":"common.addSuccessText")),c.value=!1,S("refresh")}catch(e){f().error(e.msg)}finally{i.value=!1}},T=e=>{z(e).then(a=>{Object.assign(l,a.data),a.data.dsScope?p.checkedDsScope=a.data.dsScope.split(","):p.checkedDsScope=[]})},U=()=>{E().then(e=>{p.deptData=e.data})};return $({openDialog:e=>{c.value=!0,l.roleId="",Y(()=>{n.value.resetFields()}),e&&(l.roleId=e,T(e)),U()}}),(e,a)=>{const d=u("el-input"),_=u("el-form-item"),q=u("el-form"),h=u("el-button"),F=u("el-dialog"),R=M("loading");return x(),D(F,{"close-on-click-modal":!1,title:r(l).roleId?e.$t("common.editBtn"):e.$t("common.addBtn"),width:"600",draggable:"",modelValue:r(c),"onUpdate:modelValue":a[4]||(a[4]=o=>X(c)?c.value=o:null)},{footer:t(()=>[W("span",g,[s(h,{onClick:a[3]||(a[3]=o=>c.value=!1)},{default:t(()=>[V(C(e.$t("common.cancelButtonText")),1)]),_:1}),s(h,{onClick:B,type:"primary",disabled:r(i)},{default:t(()=>[V(C(e.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:t(()=>[Q((x(),D(q,{model:r(l),rules:r(N),"label-width":"90px",ref_key:"dataFormRef",ref:n},{default:t(()=>[s(_,{label:e.$t("sysrole.roleName"),prop:"roleName"},{default:t(()=>[s(d,{placeholder:e.$t("sysrole.please_enter_a_role_name"),clearable:"",modelValue:r(l).roleName,"onUpdate:modelValue":a[0]||(a[0]=o=>r(l).roleName=o)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),s(_,{label:e.$t("sysrole.roleCode"),prop:"roleCode"},{default:t(()=>[s(d,{placeholder:e.$t("sysrole.please_enter_the_role_Code"),disabled:r(l).roleId!=="",clearable:"",modelValue:r(l).roleCode,"onUpdate:modelValue":a[1]||(a[1]=o=>r(l).roleCode=o)},null,8,["placeholder","disabled","modelValue"])]),_:1},8,["label"]),s(_,{label:e.$t("sysrole.roleDesc"),prop:"roleDesc"},{default:t(()=>[s(d,{placeholder:e.$t("sysrole.please_enter_the_role_description"),maxlength:"150",rows:"3",type:"textarea",modelValue:r(l).roleDesc,"onUpdate:modelValue":a[2]||(a[2]=o=>r(l).roleDesc=o)},null,8,["placeholder","modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[R,r(i)]])]),_:1},8,["title","modelValue"])}}})});export{Z as __tla,I as default};
